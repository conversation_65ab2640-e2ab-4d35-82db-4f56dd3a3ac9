import type { Tick } from 'chart.js'
import { format } from 'date-fns'
import { TickMarkType } from 'lightweight-charts'
import { z } from 'zod'

export function formatNameFromString(input: string) {
  // Split string by underscore and map each part to capitalize the first letter
  return input
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

export function splitFullname(fullName: string) {
  const words = fullName.split(' ')
  const divide = Math.ceil(words.length / 2)

  return {
    firstName: words.filter((_, index) => index < divide).join(' '),
    lastName: words.filter((_, index) => index > divide - 1).join(' '),
  }
}

export function titleCase(value: string) {
  return value
    .toLowerCase()
    .split(' ')
    .map((word) => {
      return word.charAt(0).toUpperCase() + word.slice(1)
    })
    .join(' ')
}

export const capitalizeFirstLetter = (input: string) => {
  // Replace underscores with spaces and then capitalize the first letter of each word
  return input
    .replace(/_/g, ' ') // Replace all underscores (or any special character you want to target) with spaces
    .replace(/\b\w/g, (char) => char.toUpperCase()) // Capitalize the first letter of each word
    .replace(/\B\w/g, (char) => char.toLowerCase()) // Ensure the rest of the word is in lowercase
}

export function formatToSlug(input: string): string {
  return input
    .toLowerCase() // Convert the string to lowercase
    .replace(/\s+/g, '-') // Replace spaces with -
    .replace(/[^a-z0-9-]/g, '') // Remove all characters that are not a-z, 0-9, or -
    .replace(/-{2,}/g, '-') // Replace multiple - with single
    .replace(/^-+|-+$/g, '') // Trim - from start and end of string
}

export function formatXAxisCjs(value: string | number, index?: number, ticks?: Tick[]) {
  const date = new Date(value)

  if (date.getMonth() === 0) {
    return date.getFullYear()
  }

  return format(date, 'MMM')
}

export function formatXApex(value: string | number | Date) {
  value = new Date(value)

  if (value.getMonth() === 0) {
    return value.getFullYear()
  }

  return format(value, 'MMM')
}

function hasSequentialNumbers(password?: string) {
  if (!password) {
    return false
  }

  for (let i = 0; i < password.length - 1; i++) {
    const current = parseInt(password[i] || '0')
    const next = parseInt(password[i + 1] || '0')

    if (
      current + 1 === next || // ascending check
      current - 1 === next || // descending check
      current === next
    ) {
      // repeating check
      return true
    }
  }
  return false
}

export function stringNoSequntialCharacters(value: string | undefined) {
  if (!value) {
    return false
  }

  // Check for repeating characters
  for (let i = 0; i < value.length - 1; i++) {
    if (value[i] === value[i + 1]) {
      return false
    }
  }

  // Check for ascending sequential characters
  for (let i = 0; i < value.length - 1; i++) {
    if (value.charCodeAt(i) === value.charCodeAt(i + 1) - 1) {
      return false
    }
  }

  // Check for descending sequential characters
  for (let i = 0; i < value.length - 1; i++) {
    if (value.charCodeAt(i) === value.charCodeAt(i + 1) + 1) {
      return false
    }
  }

  return true
}

export function generateRandomWord(length: number) {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  const charactersLength = characters.length
  let word = ''
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charactersLength)
    word += characters[randomIndex]
  }
  return word
}

export const checkPasswordRules = z
  .string()
  .nonempty({ message: 'Password cannot be empty.' })
  .min(6, { message: 'Password at least 6 characters' })
  .refine((val) => /[0-9]/.test(val), { message: 'Password must contain at least one number' })
  .refine((val) => /[a-z]/.test(val), { message: 'Password must contain at least one lowercase letter' })
  .refine((val) => /[A-Z]/.test(val), { message: 'Password must contain at least one uppercase letter' })
  .refine((val) => /[^a-zA-Z0-9]/.test(val), {
    message: 'Password must contain at least one non-alphanumeric character (e.g. !@#$%^&*)',
  })
  .refine((val) => !hasSequentialNumbers(val), { message: 'Password must not contain sequential characters' })
  .superRefine((val, ctx) => {
    if (!val || val.length < 6) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Password must be at least 6 characters',
        path: ['minCharachter'],
      })
    }

    if (!/[0-9]/.test(val)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Password must contain at least one number',
        path: ['haveNumber'],
      })
    }

    if (!/[a-z]/.test(val)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Password must contain at least one lowercase letter',
        path: ['haveLowerCase'],
      })
    }

    if (!/[A-Z]/.test(val)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Password must contain at least one uppercase letter',
        path: ['haveUpperCase'],
      })
    }

    if (!/[^a-zA-Z0-9]/.test(val)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Password must contain at least one non-alphanumeric character (e.g. !@#$%^&*)',
        path: ['haveNonAlphaNumeric'],
      })
    }

    if (hasSequentialNumbers(val)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Password must not contain sequential characters',
        path: ['noSequentialNumber'],
      })
    }
  })

export const generateRandomHexColor = () => {
  return `#${Math.floor(Math.random() * 16777215).toString(16)}`
}

export const formatOverviewChartTimeTick = (time: string, tickMarkType: TickMarkType, locale: string) => {
  if (tickMarkType === TickMarkType.Year) {
    return new Date(time).getFullYear().toString()
  } else if (tickMarkType === TickMarkType.Month) {
    return new Date(time).toLocaleDateString(locale, { month: 'short' })
  }

  return ''
}
