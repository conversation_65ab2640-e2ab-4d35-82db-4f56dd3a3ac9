<script setup lang="ts">
import type { v2AssetCoin } from '~/server/trpc/trpc'
import type { CoinFilterCallback } from './types'
import { useRadarLineChart } from '~/composables/useRadarLineChart'

const props = withDefaults(
  defineProps<{
    api?: string
    callback?: (value: CoinFilterCallback) => void
    name: string
    count: string | number
    coinFilterLabel: string
  }>(),
  {
    api: 'v2.user.asset.finance',
    callback: undefined,
  },
)

interface CoinFilter {
  name: string
  coin: v2AssetCoin | undefined
  type: 'coin-1' | 'coin-2' | 'coin-3'
}

const route = useRoute()

const modelValue = defineModel<CoinFilter>({ required: true })

const { coin1Filter, coin2Filter, coin3Filter } = useRadarLineChart()

const { $client } = useNuxtApp()

const loading = ref(false)

const onCoinSearch = async (q: string) => {
  loading.value = true

  const data = await ($client as Record<string, any>)[props.api].query({
    terms: q,
    filter: ['CRYPTOCURRENCY', 'STABLECOIN'],
    topSlug: modelValue.value.coin?.slug,
  })

  loading.value = false

  return data.assets
}

const isGray = (symbol: string) => {
  const allFiltersCoin = []

  if (route.query?.[`${props.name}1`]) {
    allFiltersCoin.push(route.query?.[`${props.name}1`])
  }
  if (route.query?.[`${props.name}2`]) {
    allFiltersCoin.push(route.query?.[`${props.name}2`])
  }
  if (route.query?.[`${props.name}3`]) {
    allFiltersCoin.push(route.query?.[`${props.name}3`])
  }

  return allFiltersCoin.includes(symbol)
}

const filterLabel = computed(() => `${props.coinFilterLabel ?? 'coin'} ${props.count}`)

const coinTypesArray = computed(() => {
  const coinFiltersArray = [coin1Filter.value.coin, coin2Filter.value.coin, coin3Filter.value.coin]
  return coinFiltersArray.reduce<string[]>((prev, curr) => {
    if (curr) {
      prev.push(curr.type)
    }
    return prev
  }, [])
})

const onChange = (dropdownValue: any) => {
  if (coinTypesArray.value.length > 0 && dropdownValue[0].type !== coinTypesArray.value[0]) {
    return
  }
  if (modelValue.value.coin?.symbol === dropdownValue[0].symbol) {
    modelValue.value.coin = undefined
  } else {
    modelValue.value.coin = dropdownValue[0]
  }
}

const isSameCoinType = (asset: any) => {
  if (coinTypesArray.value.length === 0) {
    return false
  }
  return asset.type !== coinTypesArray.value[0]
}

watch(
  () => modelValue.value.coin,
  (value) => {
    if (props.callback) {
      props.callback(value)
    }
  },
)
</script>

<template>
  <div class="flex flex-col gap-2">
    <span class="text-neutrals-400 text-xs uppercase">{{ filterLabel }}</span>
    <USelectMenu
      :model-value="[]"
      :ui-menu="{
        base: 'overflow-x-hidden',
      }"
      :option="{
        base: 'mt-10',
      }"
      :popper="{
        placement: 'bottom-start',
      }"
      :loading="loading"
      :searchable="onCoinSearch"
      searchable-placeholder="Search name or symbol"
      clear-search-on-close
      variant="solid"
      color="black"
      :search-attributes="['name', 'symbol']"
      size="md"
      class="w-full md:min-w-[125px]"
      selected-icon=""
      multiple
      @update:model-value="onChange"
    >
      <template #label>
        <CoinDisplayName
          v-if="modelValue.coin"
          :coin="modelValue.coin"
          no-padding
          text-class="text-base"
          truncate
        />
        <span
          v-else
          class="truncate text-nowrap text-base"
        >
          Select
        </span>
      </template>

      <template #option="{ option: asset, selected }">
        <div
          class="flex min-w-0 items-center justify-between gap-x-2"
          :class="{ grayscale: selected || isGray(asset.slug) }"
        >
          <div class="flex min-w-0 items-center gap-x-2">
            <CoinDisplayName
              :coin="asset"
              no-padding
              :text-class="
                isGray(asset.slug) || isSameCoinType(asset)
                  ? 'text-neutrals-400 dark:text-neutrals-400'
                  : 'text-neutrals-700 dark:text-white'
              "
            />
          </div>
          <div
            v-if="selected || modelValue.coin?.symbol === asset.symbol"
            class="flex items-center justify-center text-lg font-semibold"
          >
            <UIcon name="i-heroicons-x-mark" />
          </div>
        </div>
      </template>
    </USelectMenu>
  </div>
</template>
