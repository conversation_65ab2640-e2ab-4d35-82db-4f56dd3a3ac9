import { CHART_TYPES } from '~/constants/charts'

export const dexMetricGroups = [
  {
    group: 'Consensus (Consensus Power)',
    color: '#FF2323',
    description: '',
    metrics: [
      {
        label: 'IP Author Distr (Gini)',
        description: `A measure of the inequality in the number of proposals submitted by different authors. A more uneven distribution indicates a higher degree of centralization. To maintain consistency across metrics, the Gini coefficient values are inverted: a score of 1 represents perfect equality (i.e. all authors contributed equally), while a score of 0 represents maximal inequality (i.e. all proposals were submitted by a single author).`,
        compareUnit: 'ipAuthDistrGini',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'IP Participant Div (Shannon)',
        description: `A measure of the diversity of participation among commenters/discussants. Higher entropy values suggest more distributed (less centralized) participation.`,
        compareUnit: 'ipParticipantDivGini',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'IP Author Infl Conc (HHI)',
        description: `A measure of the concentration of influence among authors. This is calculated by using the Herfindahl-Hirschman Index (HHI), where the proportion of successful proposals per author is treated as their "market share." For consistency across metrics, the HHI values are inverted: a higher score indicates less concentration (i.e. influence is more evenly distributed), while a lower score indicates greater concentration of influence among fewer authors.`,
        compareUnit: 'ipAuthorInflConcHHI',
        multipleCompareUnit: true,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'IP Gov (Ordinal)',
        description: `An assessment of the level of centralization in the governance process of IPs, focusing on how decision-making authority is distributed among participants.`,
        compareUnit: 'ipGovOrdinal',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.DECISION_TREE,
      },
    ],
  },
  {
    group: 'Application (Reference Client Development)',
    color: '#3EA95B',
    description: '',
    metrics: [
      {
        label: 'RCD Dev Distr (Gini)',
        description: `A measure of the inequality among developers in terms of their contributions to the reference client's codebase. To maintain consistency across metrics, the Gini coefficient values are inverted: a score of 1 represents perfect equality (i.e. all developers contributed equally), while a score of 0 represents maximal inequality (i.e. all contributions were made by a single developer).`,
        compareUnit: 'rcpDevDistrGini',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'RCD Participant Div (Shannon)',
        description: `A measure of the diversity of participation among commenters/discussants. Higher entropy values suggest more distributed (less centralized) participation.`,
        compareUnit: 'rcpParticipantDivShannon',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'RCD Dev Infl Conc (HHI)',
        description: `A measure of the proportion of contributions to the reference client development made by a small group of developers, quantifying their dominance and influence within the project. For consistency across metrics, the HHI values are inverted: a score of 1 indicates low concentration (i.e. contributions are more evenly distributed), while a score of 0 indicates high concentration (i.e. a small group dominates development).`,
        compareUnit: 'rcpDevInflConcHHI',
        multipleCompareUnit: true,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'RCD Revr Power Conc (HHI)',
        description: `An assessment of the concentration of decision-making power among those who review changes to the reference client's code. For consistency across metrics, the HHI values are inverted: a higher score indicates more distributed influence among reviewers, while a lower score suggests that a few reviewers hold significant decision-making power, indicating potential centralization in the code review process.`,
        compareUnit: 'rcdRevrPowerConcHHI',
        multipleCompareUnit: true,
        chartType: CHART_TYPES.LINE_CHART,
      },
    ],
  },
  {
    group: 'Governance (Improvement Proposal)',
    color: '#8B5CF6',
    description: '',
    metrics: [
      {
        label: 'Consensus Power ≠ (Gini)',
        description: `A measure of the inequality in the distribution of consensus power among participants, such as miners or validators, within the blockchain network. For consistency across metrics, the Gini coefficient values are inverted: a higher score indicates a more even distribution of consensus power (i.e. greater decentralization), while a lower score indicates a more uneven distribution (i.e. greater centralization).`,
        compareUnit: 'consensusPowerNeGini',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'Consensus Power ≠ (Theil)',
        description: `An assessment of the degree of disparity in consensus power among network participants. The Theil index is used to quantify this imbalance, with values inverted for consistency across metrics: a higher score indicates a more equal distribution of consensus power (i.e. greater decentralization), while a lower score reflects a greater disparity, where a small number of participants control a disproportionately large share, signalling centralization.`,
        compareUnit: 'consensusPowerNeTheil',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'Consensus Power Conc (Nakamoto)',
        description: `A quantification of the minimum number of participants (such as the top few mining pools or validators) that collectively control more than 1/3 of the total consensus power. A lower Nakamoto Coefficient indicates higher centralization, as fewer participants have more control.`,
        compareUnit: 'consensusPowerConcNakamoto',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
      {
        label: 'Consensus Power Conc (HHI)',
        description: `A measure of the concentration of consensus power based on the Herfindahl-Hirschman Index (HHI) calculated across participants. For consistency across metrics, the HHI values are inverted: a higher score reflects a more even distribution of consensus power (i.e. less concentration), while a lower score indicates that consensus power is concentrated among fewer participants, signalling potential centralization risks.`,
        compareUnit: 'consensusPowerConcHHI',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.LINE_CHART,
      },
    ],
  },
  {
    group: 'Economics (Treasury Management)',
    color: '#FF8D24',
    description: '',
    metrics: [
      {
        label: 'Coin Distr (Ordinal)',
        description: `An evaluation of the rules and governance mechanisms that determine how many coins are mined, when they are mined, and to whom they are distributed, assessing how these coins are released into the market and become part of the circulating supply. This focuses on the centralization of decision-making in controlling the allocation and distribution processes.`,
        compareUnit: 'coinDistrOrdinal',
        multipleCompareUnit: false,
        chartType: CHART_TYPES.DECISION_TREE,
      },
    ],
  },
]

const getMetricGroup = (index: number) => {
  const metric = dexMetrics[index]

  const group = dexMetricGroups.find((group) => {
    return group.metrics.find((_metric) => {
      return _metric.label === metric
    })
  })

  return group
}

const getMetricObj = (index: number) => {
  const metric = dexMetrics[index]
  const metricObjArray = dexMetricGroups.map((group) => group.metrics).flat()

  return metricObjArray.find((metricObj) => metricObj.label === metric)
}

export const dexCompareKeyMetrics = dexMetricGroups
  .map((group) => group.metrics.map((metric) => metric.compareUnit).flat())
  .flat()

export const dexMetrics = dexMetricGroups.map((group) => group.metrics.map((metric) => metric.label).flat()).flat()

export const dexMetricColor = (index: number) => {
  const group = getMetricGroup(index)

  return group!.color
}

export const getDexMetricObjKey = (key: string, index: number) => {
  const group = getMetricObj(index)

  return (group as Record<string, unknown>)[key]
}
