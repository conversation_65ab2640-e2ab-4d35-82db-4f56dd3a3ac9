<script setup lang="ts">
import { format } from 'date-fns'

// Constants
import { LEGEND_MOOD_TRUST, LEGEND_PRICE_VOLUME, LEGEND_PLATFORMS } from '~/constants/legends'
import { ONBOARDING_STABLE_SUMMARY_PAGE, ONBOARDING_PATHS } from '~/constants/onboarding'
import { ASSET_STABLECOIN_TOOLTIPS } from '~/constants/common-tooltips'

// Helpers
import { titleCase } from '~/helper/string-helper'
import { numericDisplay } from '~/helper/number-helper'

// Types
import type { AssetType } from '~/types/appTypes'
import type { v2AssetList } from '~/server/trpc/trpc'
import type { OverviewChartDataset } from '~/components/molecule/OverviewChart/OverviewChart.vue'

const { $client, $router } = useNuxtApp()
const toast = useToast()
const route = useRoute()
const router = useRouter()

const selectedAsset = useState<v2AssetList['data'][0] | undefined>()
const isScrolling = ref(false)
const maxVolumeChart = ref(0)
const { isFeatureAllowed } = useFeature()

let scrollTimeout: NodeJS.Timeout | null = null

const onboardingSteps = computed(() => {
  const steps = [...ONBOARDING_STABLE_SUMMARY_PAGE]

  return steps
})

const lastCalculatedDate = computed(() =>
  summaryData.value?.last_calculation_date
    ? format(new Date(summaryData.value?.last_calculation_date), 'MMM dd, yyyy')
    : '',
)

const {
  data: summaryData,
  status: summaryStatus,
  refresh: refreshSummaryData,
} = await useLazyAsyncData(
  'tokenStableOverview',
  async () => {
    try {
      const res = await $client.v2.user.indices.overviewStablecoin.query((route.params.slug || '').toString())
      selectedAsset.value = res.asset as v2AssetList['data'][0]
      return res
    } catch (error: any) {
      let message = 'Something went wrong'

      if (error?.message === 'ASSET_NOT_FOUND') {
        message = 'Asset not found'
      }

      toast.add({
        title: 'Potential Issue with Data',
        description: message,
        icon: 'i-heroicons-exclamation-triangle',
        color: 'amber',
      })

      setTimeout(() => {
        $router.push('/')
      }, 3000)

      return null
    }
  },
  {
    immediate: false,
    server: false,
  },
)

const {
  data: onboardingData,
  status: onboardingStatus,
  refresh: refreshOnboardingStatus,
} = await useLazyAsyncData(
  'tokenStableOverviewOnboarding',
  async () => {
    return await $client.v2.user.account.onboardingStatus.query(ONBOARDING_PATHS.STABLECOIN_SUMMARY)
  },
  {
    immediate: false,
    server: false,
  },
)

const isOverviewPending = computed(() => {
  return onboardingStatus.value === 'pending' || summaryStatus.value === 'pending'
})

const chartDataMoodTrust = computed<OverviewChartDataset[]>(() => {
  const moodSeries = (summaryData.value?.mood ?? []).map(([time, value]) => ({
    time: format(new Date(time as number), 'yyyy-MM-dd'),
    value: value ?? null,
  }))

  const trustSeries = (summaryData.value?.trust ?? []).map(([time, value]) => ({
    time: format(new Date(time as number), 'yyyy-MM-dd'),
    value: value ?? null,
  }))

  if (moodSeries.length === 0 && trustSeries.length === 0) {
    return []
  }

  return [
    {
      type: 'line',
      name: 'Mood',
      color: 'rgba(0,163,255,1)',
      data: moodSeries,
      showLastMarker: true,
      markerBackgroundColor: 'rgba(0,163,255,1)',
      markerShadowColor: 'rgba(0,163,255,0.35)',
    },
    {
      type: 'line',
      name: 'Trust',
      color: 'rgba(79,189,109,1)',
      data: trustSeries,
      showLastMarker: true,
      markerBackgroundColor: 'rgba(79,189,109,1)',
      markerShadowColor: 'rgba(79,189,109,0.35)',
    },
  ]
})

const chartDataMarketcapVolume = computed<Record<string, OverviewChartDataset[]>>(() => {
  const marketCapSeries = (summaryData.value?.marketcap ?? []).map(([time, value]) => ({
    time: format(new Date(time as number), 'yyyy-MM-dd'),
    value: value ?? null,
  }))

  maxVolumeChart.value = Math.max(...(summaryData.value?.volume?.map(([, value]) => value ?? 0) ?? [0]))

  const volumeSeries = (summaryData.value?.volume ?? []).map(([time, value]) => ({
    time: format(new Date(time || 0), 'yyyy-MM-dd'),
    value: value ?? null,
  }))

  return {
    marketCap: [
      {
        type: 'area',
        name: 'Market Cap',
        color: '#7C3AED',
        data: marketCapSeries,
        showLastMarker: true,
        markerBackgroundColor: 'rgba(124,58,237,1)',
        markerShadowColor: 'rgba(124,58,237,0.35)',
        areaTopColor: 'rgba(124,58,237,0.50)',
        areaBottomColor: 'rgba(124,58,237,0.20)',
      },
    ],
    volume: [
      {
        type: 'histogram',
        name: 'Volume',
        color: '#C3C3C3',
        data: volumeSeries,
        showLastMarker: true,
      },
    ],
  }
})
const isChartDataMarketcapVolumeEmpty = computed(() => {
  return (
    chartDataMarketcapVolume.value?.marketCap?.[0]?.data?.length &&
    chartDataMarketcapVolume.value?.volume?.[0]?.data?.length
  )
})

const chartDataLiquidityCost = computed<OverviewChartDataset[]>(() => {
  const cexSeries = (summaryData.value?.avg_liqudity.cex ?? []).map(([time, value]) => ({
    time: format(new Date(time as number), 'yyyy-MM-dd'),
    value: value ?? null,
  }))
  const dexSeries = (summaryData.value?.avg_liqudity.dex ?? []).map(([time, value]) => ({
    time: format(new Date(time as number), 'yyyy-MM-dd'),
    value: value ?? null,
  }))

  if (cexSeries.length === 0 && dexSeries.length === 0) {
    return []
  }

  return [
    {
      type: 'line',
      name: 'CEX',
      color: 'rgba(255,0,0,1)',
      data: cexSeries,
      showLastMarker: true,
      markerBackgroundColor: 'rgba(255,0,0,1)',
      markerShadowColor: 'rgba(255,0,0,0.35)',
    },
    {
      type: 'line',
      name: 'DEX',
      color: 'rgba(3,120,35,1)',
      data: dexSeries,
      showLastMarker: true,
      markerBackgroundColor: 'rgba(3,120,35,1)',
      markerShadowColor: 'rgba(3,120,35,0.35)',
    },
  ]
})

const chartDataPrice = computed<OverviewChartDataset[]>(() => {
  const priceSeries = (summaryData.value?.price ?? []).map(([time, value]) => ({
    time: format(new Date(time || 0), 'yyyy-MM-dd'),
    value: value ?? null,
  }))

  if (priceSeries.length === 0) {
    return []
  }

  return [
    {
      type: 'area',
      name: 'Price',
      color: 'rgba(255,165,0,1)',
      data: priceSeries,
      showLastMarker: true,
      markerBackgroundColor: 'rgba(255,165,0,1)',
      markerShadowColor: 'rgba(255,165,0,0.35)',
      areaTopColor: 'rgba(255,165,0,0.50)',
      areaBottomColor: 'rgba(255,165,0,0.20)',
    },
  ]
})

const chartDataMessage = computed<OverviewChartDataset[]>(() => {
  const redditSeries = (summaryData.value?.messages.reddit ?? []).map(([time, value]) => ({
    time: format(new Date(time), 'yyyy-MM-dd'),
    value,
  }))

  const telegramSeries = (summaryData.value?.messages.telegram ?? []).map(([time, value]) => ({
    time: format(new Date(time), 'yyyy-MM-dd'),
    value,
  }))

  if (redditSeries.length === 0 && telegramSeries.length === 0) {
    return []
  }

  return [
    {
      type: 'histogram',
      name: 'Reddit',
      color: 'rgba(234, 92, 21, 1)',
      data: redditSeries,
    },
    {
      type: 'histogram',
      name: 'Telegram',
      color: 'rgba(0, 125, 207, 1)',
      data: telegramSeries,
    },
  ]
})

const chartDataCommunity = computed<OverviewChartDataset[]>(() => {
  const redditSeries = (summaryData.value?.community?.reddit ?? []).map(([time, value]) => ({
    time: format(new Date(time), 'yyyy-MM-dd'),
    value,
  }))
  const telegramSeries = (summaryData.value?.community?.telegram ?? []).map(([time, value]) => ({
    time: format(new Date(time), 'yyyy-MM-dd'),
    value,
  }))

  if (redditSeries.length === 0 && telegramSeries.length === 0) {
    return []
  }

  return [
    {
      type: 'line',
      name: 'Reddit',
      color: 'rgba(234,92,21,1)',
      data: redditSeries,
      showLastMarker: true,
      markerBackgroundColor: 'rgba(234,92,21,1)',
      markerShadowColor: 'rgba(234,92,21,0.35)',
    },
    {
      type: 'line',
      name: 'Telegram',
      color: 'rgba(0,125,207,1)',
      data: telegramSeries,
      showLastMarker: true,
      markerBackgroundColor: 'rgba(0,125,207,1)',
      markerShadowColor: 'rgba(0,125,207,0.35)',
    },
  ]
})

const searchAsset = async (q: string) => {
  const assets = await $client.v2.user.asset.list.query({ terms: q })
  return assets.data
}

const finishOnboarding = async () => {
  await $client.v2.user.account.setOnboardingStatus.mutate({ path: ONBOARDING_PATHS.STABLECOIN_SUMMARY })
}

const handleScroll = () => {
  isScrolling.value = true

  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }

  scrollTimeout = setTimeout(() => {
    isScrolling.value = false
  }, 300) // 150ms timeout after scrolling stops
}

const onChartClick = () => {
  const url = `/community/mood/${(route.params.slug || '').toString()}`
  if (!isScrolling.value) {
    return url
  }
  return ''
}

watch(summaryData, (overviewData) => {
  if (overviewData) {
    selectedAsset.value = overviewData.asset as v2AssetList['data'][0]
  }
})

const handleAssetChange = async (asset: AssetType) => {
  if (asset?.type === 'STABLECOIN') {
    router.push(`/stablecoin/${asset?.slug}`)
  } else {
    router.push(`/crypto/${asset?.slug}`)
  }
}

onMounted(async () => {
  window.addEventListener('touchmove', handleScroll)

  await Promise.all([refreshSummaryData(), refreshOnboardingStatus()])
})

onBeforeUnmount(() => {
  window.removeEventListener('touchmove', handleScroll)
})
</script>

<template>
  <div class="flex flex-col gap-3 p-5">
    <Head>
      <Title>{{ summaryData?.name }} Community Sentiment, Market Dynamics, and CEX/DEX Liquidity live charts</Title>
    </Head>

    <div class="mb-5 flex flex-col gap-3 md:flex-row md:items-center md:justify-between">
      <AssetSelector
        v-model="selectedAsset"
        :search-assets="searchAsset"
        :loading="isOverviewPending"
        @change="handleAssetChange"
      />

      <span class="text-dark-grey text-base font-medium">Last Update: {{ lastCalculatedDate }}</span>
    </div>

    <OverviewSkeleton v-if="isOverviewPending" />
    <div
      v-else
      id="onboarding-step-1"
      class="flex flex-col gap-3"
    >
      <div class="grid grid-cols-1 gap-3 md:grid-cols-12">
        <div class="col-span-1 md:col-span-8">
          <UCard class="h-full">
            <div class="flex flex-col items-start gap-3 md:flex-row">
              <div class="flex items-center gap-x-2 md:min-w-[80px]">
                <img
                  :src="summaryData?.logo ?? ''"
                  width="80"
                  height="80"
                  class="block h-[50px] w-[50px] md:h-[80px] md:!w-[80px]"
                />

                <h3 class="mb-1 text-3xl font-bold text-black md:hidden dark:text-white">
                  {{ summaryData?.name }}
                </h3>
              </div>

              <div class="text-dark-grey flex flex-col flex-wrap text-sm">
                <h3 class="mb-1 hidden text-3xl font-bold text-black md:inline-block dark:text-white">
                  {{ summaryData?.name }}
                </h3>
                <p class="dark:text-white">
                  {{ summaryData?.description }}
                </p>

                <div class="mt-4 flex items-center gap-x-3">
                  <UButton
                    v-if="summaryData?.website"
                    color="black"
                    leading-icon="i-heroicons-globe-alt"
                    label="Website"
                    :to="summaryData?.website ?? undefined"
                    target="_blank"
                  />
                  <UButton
                    v-for="(link, linkIndex) in (summaryData?.links ?? []) as any[]"
                    :key="linkIndex"
                    leading-icon="i-mdi-github"
                    :to="link.url ?? undefined"
                    :label="titleCase(link.platform)"
                  />
                </div>
              </div>
            </div>
          </UCard>
        </div>
        <div class="col-span-1 flex-col gap-3 md:col-span-4">
          <UCard class="mb-3">
            <div class="mb-4 flex items-center gap-x-1">
              <h3 class="text-neutrals-400 text-base font-medium dark:text-white">Price</h3>
              <InformationPopover icon-class="mt-1.5">
                <TooltipContent>
                  <p>
                    The market price of a single unit of a crypto asset, denominated in United States Dollars (USD).
                    This reflects the trading price from various exchanges, offering a snapshot of a crypto asset's
                    value on the current day.
                  </p>
                </TooltipContent>
              </InformationPopover>
            </div>
            <div class="flex items-center justify-center">
              <span class="text-center text-4xl font-bold text-black dark:text-white">{{
                summaryData?.overview.price
              }}</span>
            </div>
          </UCard>

          <div class="grid grid-cols-1 gap-3 lg:grid-cols-2">
            <CheckFeature
              :allowed="!!isFeatureAllowed('sentiment_metrics.mood_ranking')"
              small
            >
              <UCard>
                <div class="mb-4 flex items-center gap-x-1">
                  <h3 class="text-neutrals-400 text-base font-medium dark:text-white">Mood Ranking</h3>
                  <InformationPopover icon-class="mt-1.5">
                    <TooltipContent>
                      <p v-html="ASSET_STABLECOIN_TOOLTIPS.mood_ranking" />
                    </TooltipContent>
                  </InformationPopover>
                </div>
                <div class="flex items-center justify-center">
                  <span class="text-center text-4xl font-bold text-black dark:text-white"
                    >#{{ summaryData?.overview.mood_rank }}</span
                  >
                </div>
              </UCard>
            </CheckFeature>
            <CheckFeature
              :allowed="!!isFeatureAllowed('sentiment_metrics.trust_ranking')"
              small
            >
              <UCard>
                <div class="mb-4 flex items-center gap-x-1">
                  <h3 class="text-neutrals-400 text-base font-medium dark:text-white">Trust Ranking</h3>
                  <InformationPopover icon-class="mt-1.5">
                    <TooltipContent>
                      <p v-html="ASSET_STABLECOIN_TOOLTIPS.trust_ranking" />
                    </TooltipContent>
                  </InformationPopover>
                </div>
                <div class="flex items-center justify-center">
                  <span class="text-center text-4xl font-bold text-black dark:text-white"
                    >#{{ summaryData?.overview.trust_rank }}</span
                  >
                </div>
              </UCard>
            </CheckFeature>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 gap-3 md:grid-cols-3">
        <CheckFeature
          :allowed="!!isFeatureAllowed('sentiment_metrics.messages')"
          small
        >
          <UCard>
            <div class="mb-4 flex items-center gap-x-1">
              <h3 class="text-neutrals-400 text-base font-medium dark:text-white"># Messages (14d)</h3>
              <InformationPopover icon-class="mt-1.5">
                <TooltipContent>
                  <p>
                    The total number of posts, comments, and messages exchanged across the tracked communities of a
                    crypto asset over the last 14 days. This shows the overall level of activity and interaction within
                    these communities.
                  </p>
                </TooltipContent>
              </InformationPopover>
            </div>
            <div class="flex items-center justify-center">
              <span class="text-center text-4xl font-bold text-black dark:text-white">
                {{ summaryData?.overview.number_messages }}
              </span>
            </div>
          </UCard>
        </CheckFeature>
        <CheckFeature
          :allowed="!!isFeatureAllowed('sentiment_metrics.bots_tracker')"
          small
        >
          <UCard>
            <div class="mb-4 flex items-center gap-x-1">
              <h3 class="text-neutrals-400 text-base font-medium dark:text-white"># Bots (14d)</h3>
              <InformationPopover icon-class="mt-1.5">
                <TooltipContent>
                  <p v-html="ASSET_STABLECOIN_TOOLTIPS.bots" />
                </TooltipContent>
              </InformationPopover>
            </div>
            <div class="flex items-center justify-center">
              <span class="text-center text-4xl font-bold text-black dark:text-white">
                {{ summaryData?.overview.number_bots }}
              </span>
            </div>
          </UCard>
        </CheckFeature>
        <CheckFeature
          :allowed="!!isFeatureAllowed('sentiment_metrics.vulgarity_index')"
          small
        >
          <UCard>
            <div class="mb-4 flex items-center gap-x-1">
              <h3 class="text-neutrals-400 text-base font-medium dark:text-white">Vulgarity Index</h3>
              <InformationPopover icon-class="mt-1.5">
                <TooltipContent>
                  <p v-html="ASSET_STABLECOIN_TOOLTIPS.vulgarity_index" />
                </TooltipContent>
              </InformationPopover>
            </div>
            <div class="flex items-center justify-center">
              <span class="text-center text-4xl font-bold text-black dark:text-white">
                {{ summaryData?.overview.number_bad_word }}
              </span>
            </div>
          </UCard>
        </CheckFeature>
      </div>
    </div>

    <div
      id="onboarding-step-2"
      class="flex scroll-mt-72 flex-col gap-3"
    >
      <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
        <CheckFeature
          :allowed="
            !!isFeatureAllowed('sentiment_metrics.mood_index_level_1') &&
            !!isFeatureAllowed('sentiment_metrics.trust_index_level_1')
          "
          :loading="isOverviewPending"
        >
          <div
            id="onboarding-step-3"
            class="col-span-1 scroll-mt-72"
          >
            <CardChart
              title="Mood Index & Trust Index"
              :subtitle="ASSET_STABLECOIN_TOOLTIPS.mood_trust_index"
              :legends="LEGEND_MOOD_TRUST"
              :is-loading="isOverviewPending"
              body-class="chart-container v1-4xl:min-h-[510px] h-[338px] 2xl:min-h-[400px]"
            >
              <NuxtLink
                :href="onChartClick()"
                external
              >
                <OverviewChart :datasets="chartDataMoodTrust" />
              </NuxtLink>
            </CardChart>
          </div>
        </CheckFeature>
        <div
          id="onboarding-step-4"
          class="col-span-1 scroll-mt-72"
        >
          <CardChart
            title="Market Cap & Volume"
            :subtitle="ASSET_STABLECOIN_TOOLTIPS.maket_cap_volume"
            :legends="[
              {
                label: 'Market Cap',
                colorClass: 'bg-accent-purple-600',
              },
              {
                label: 'Volume',
                colorClass: 'bg-neutrals-300',
              },
            ]"
            :is-loading="isOverviewPending"
            body-class="v1-4xl:min-h-[510px] h-[338px] 2xl:min-h-[400px]"
          >
            <NuxtLink
              :href="`/financial?asset1=${route.params.slug}&metric=marketCap&size=100`"
              external
            >
              <NoDataAvailable
                v-if="isChartDataMarketcapVolumeEmpty"
                class="h-full"
              />
              <div
                v-else
                class="flex h-full flex-col gap-3"
              >
                <div class="flex-1">
                  <OverviewChart
                    :datasets="chartDataMarketcapVolume.marketCap || []"
                    :price-formatter="(price) => '$' + numericDisplay(price)"
                    hide-timescale
                  />
                </div>
                <div class="h-[100px] 2xl:h-[120px]">
                  <OverviewChart
                    :datasets="chartDataMarketcapVolume.volume || []"
                    :price-formatter="(price) => '$' + numericDisplay(price)"
                    :auto-scale-info-provider="
                      maxVolumeChart > 0
                        ? () => ({
                            priceRange: {
                              minValue: 0,
                              maxValue: maxVolumeChart * 2 * 450000000000, // Need to adjust scale, you can modifying this value
                            },
                            margins: {
                              above: 10,
                              below: 10,
                            },
                          })
                        : undefined
                    "
                  />
                </div>
              </div>
            </NuxtLink>
          </CardChart>
        </div>
      </div>

      <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
        <div
          id="onboarding-step-5"
          class="col-span-1 scroll-mt-60"
        >
          <CardChart
            key="avg-liquidity-cost-chart"
            title="Liq Cost"
            :subtitle="`The average percentage of a crypto asset's market price paid to execute a market order of 100.00 USD on the tracked Centralized Exchanges (CEXs) and Decentralized Exchanges (DEXs). This represents the average cost of trading a specific crypto asset based on the Marginal Cost of Immediacy (MCI) metric.`"
            :legends="[
              {
                label: 'CEX',
                colorClass: 'bg-accent-red-600',
              },
              {
                label: 'DEX',
                colorClass: 'bg-accent-green-700',
              },
            ]"
            :is-loading="isOverviewPending"
            body-class="v1-4xl:min-h-[510px] h-[338px] 2xl:min-h-[400px]"
          >
            <NuxtLink
              :href="onChartClick()"
              external
            >
              <OverviewChart
                :datasets="chartDataLiquidityCost"
                :price-formatter="(price) => numericDisplay(price) + '%'"
              />
            </NuxtLink>
          </CardChart>
        </div>
        <div
          id="onboarding-step-6"
          class="col-span-1 scroll-mt-60"
        >
          <CardChart
            key="price-chart"
            title="Price"
            :subtitle="`Price is the market price of a single unit of a crypto asset, denominated in United States Dollars (USD). This reflects the trading price from various exchanges, offering a snapshot of a crypto asset's value daily.`"
            :legends="[LEGEND_PRICE_VOLUME[0]]"
            :is-loading="isOverviewPending"
            body-class="v1-4xl:min-h-[510px] h-[338px] 2xl:min-h-[400px]"
            enable-log-button
          >
            <template #default="{ logarithmic }">
              <NuxtLink
                :href="onChartClick()"
                external
              >
                <OverviewChart
                  :logarithmic="logarithmic"
                  :datasets="chartDataPrice"
                  :price-formatter="(price) => '$' + numericDisplay(price, 4)"
                />
              </NuxtLink>
            </template>
          </CardChart>
        </div>
      </div>

      <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
        <CheckFeature
          :allowed="!!isFeatureAllowed('sentiment_metrics.messages')"
          :loading="isOverviewPending"
        >
          <div
            id="onboarding-step-7"
            class="col-span-1 scroll-mt-60"
          >
            <CardChart
              key="message-chart"
              title="# Messages (14d)"
              subtitle="The total number of posts, comments, and messages exchanged across the tracked communities of a crypto asset over a rolling 14-day period. This shows the overall level of activity and interaction within these communities."
              :legends="LEGEND_PLATFORMS"
              :is-loading="isOverviewPending"
              body-class="v1-4xl:min-h-[510px] h-[338px] 2xl:min-h-[400px]"
            >
              <NuxtLink
                :href="onChartClick()"
                external
              >
                <OverviewChart
                  :datasets="chartDataMessage"
                  :decimals="0"
                />
              </NuxtLink>
            </CardChart>
          </div>
        </CheckFeature>
        <CheckFeature
          :allowed="!!isFeatureAllowed('sentiment_metrics.community_size')"
          :loading="isOverviewPending"
        >
          <div
            id="onboarding-step-8"
            class="col-span-1 scroll-mt-60"
          >
            <CardChart
              key="community-size-chart"
              title="Community Size"
              subtitle="The total number of active and inactive members across the tracked communities of a crypto asset. This shows the overall size and potential reach of these communities."
              :legends="LEGEND_PLATFORMS"
              :is-loading="isOverviewPending"
              body-class="chart-container v1-4xl:min-h-[510px] h-[338px] 2xl:min-h-[400px]"
            >
              <NuxtLink
                :href="onChartClick()"
                external
              >
                <OverviewChart
                  :datasets="chartDataCommunity"
                  :decimals="0"
                />
              </NuxtLink>
            </CardChart>
          </div>
        </CheckFeature>
      </div>
    </div>

    <ClientOnly>
      <Onboarding
        v-if="summaryStatus !== 'pending' && onboardingStatus !== 'pending' && !onboardingData"
        :steps="onboardingSteps"
        :visible="!onboardingData"
        @on-finish="finishOnboarding"
      />
    </ClientOnly>
  </div>
</template>
