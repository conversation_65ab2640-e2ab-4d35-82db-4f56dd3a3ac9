<script setup lang="ts">
import { LineStyle, type Time } from 'lightweight-charts'
import { format } from 'date-fns'
import domtoimage from 'dom-to-image'
import jsPDF from 'jspdf'
import FileSaver from 'file-saver'

// Helpers
import { numericDisplay } from '~/helper/number-helper'
import { formatToSlug } from '~/helper/string-helper'
import { titleCase } from '~/helper/string-helper'
import { blobToBase64 } from '~/helper/utilities-helper'

// Constants
import { HTTP_STATUS } from '~/constants/error'

// Styles
import 'range-slider-input/dist/style.css'

// Types
import type { SeriesLine } from 'app-types/community'

const { $colorMode, $apiClient } = useNuxtApp()
const route = useRoute()
const config = useRuntimeConfig()
const toast = useToast()

const { topic = '' } = route.params as Record<string, string>

const {
  chart,
  zoomViews,
  log,
  tools,
  renderChart,
  addAreaSeries,
  renderBottomChart,
  setPriceScaleVisibility,
  destroyChart,
} = useLightChart()
const { onBack } = useBackHandler()
const { isMobile } = useDeviceScreen()
const { isFeatureAllowed } = useFeature()

const isShareModalOpen = ref(false)

const topicChartRef = ref<HTMLElement>()
const topicChartBottomRef = ref<HTMLElement>()
const exportElement = ref<HTMLElement>()

const seriesLineStyle = ref<SeriesLine>({
  all: {
    topColor: 'rgba(195, 195, 195, 0.50)',
    bottomColor: 'rgba(195, 195, 195, 0.20)',
    lineColor: 'rgba(195, 195, 195, 1)',
  },
  telegram: {
    topColor: 'rgba(0, 125, 207, 0.50)',
    bottomColor: 'rgba(0, 125, 207, 0.20)',
    lineColor: 'rgba(0, 125, 207, 1)',
  },
  reddit: {
    topColor: 'rgba(237, 114, 59, 0.50)',
    bottomColor: 'rgba(237, 114, 59, 0.20)',
    lineColor: 'rgba(237, 114, 59,1)',
  },
  x: {
    topColor: 'rgba(91, 33, 182, 0.50)',
    bottomColor: 'rgba(91, 33, 182, 0.20)',
    lineColor: 'rgba(91, 33, 182, 1)',
  },
})

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})

const fullPath = computed(() => `${config.public.appUrl}/community/topic/${topic}`)

const topicName = computed(() => titleCase(topic.split('-').join(' ')))

const { data, status, error } = useLazyAsyncData(
  `standalone-topic-chart-${topic}`,
  async () => {
    const { response, data, error } = await $apiClient.GET('/nodiens/api/v1/topic-trend/{topic}', {
      params: { path: { topic } },
    })

    if (response.status === HTTP_STATUS.INTERNAL_SERVER_ERROR) {
      throw error
    }

    return data?.payload ?? null
  },
  {
    server: false,
  },
)

const isLoading = computed(() => status.value === 'pending')

const isChartEmpty = computed(() => {
  return !data.value || data.value.analytics.length === 0
})

const initChart = () => {
  if (chart.value === undefined) {
    renderChart(
      topicChartRef,
      {
        grid: {
          horzLines: {
            color: '#C3C3C3',
            style: LineStyle.Dashed,
          },
          vertLines: {
            visible: false,
          },
        },
        crosshair: {
          horzLine: {
            visible: false,
            labelVisible: false,
          },
        },
        rightPriceScale: {
          visible: false,
        },
        leftPriceScale: {
          visible: isMobile.value ? false : true,
          borderVisible: false,
          entireTextOnly: true,
          scaleMargins: {
            bottom: 0.01,
          },
        },
        timeScale: {
          visible: false,
          borderVisible: false,
          borderColor: 'rgba(197, 203, 206, 1)',
          fixRightEdge: true,
          rightOffset: 5,
          lockVisibleTimeRangeOnResize: true,
        },
        handleScale: {
          pinch: false,
          mouseWheel: false,
          axisPressedMouseMove: false,
        },
        handleScroll: {
          mouseWheel: false,
          vertTouchDrag: false,
          horzTouchDrag: false,
          pressedMouseMove: false,
        },
        localization: {
          priceFormatter: (price: any) => numericDisplay(price),
        },
        ...(data.value?.startDate && { startCalculation: format(data.value.startDate, 'yyyy-MM-dd') }),
        ...(data.value?.endDate && { latestCalculation: format(data.value.endDate, 'yyyy-MM-dd') }),
      },
      darkMode.value,
    )
  }

  // Add Area Series
  if (Array.isArray(data.value?.analytics) && chart.value) {
    data.value.analytics.forEach((item) => {
      const areaInstance = addAreaSeries(item.platform, {
        priceScaleId: 'left',
        topColor: seriesLineStyle.value[item.platform.toLowerCase()]?.topColor,
        bottomColor: seriesLineStyle.value[item.platform.toLowerCase()]?.bottomColor,
        lineColor: seriesLineStyle.value[item.platform.toLowerCase()]?.lineColor,
        lineWidth: 2,
      })

      if (areaInstance) {
        const chartSeriesData = item.history.map(([time, value]) => ({
          time: format(time, 'yyyy-MM-dd'),
          value,
          customValues: {
            name: titleCase(item.platform),
          },
        }))

        if (chartSeriesData) {
          areaInstance.setData(chartSeriesData)
          areaInstance.applyOptions({
            visible: true,
            lastValueVisible: false,
            priceLineVisible: false,
          })
        }
      }
    })

    renderBottomChart(topicChartBottomRef, '#topic-range-slider', darkMode.value)
    tools.prepareTooltip({
      darkMode: darkMode.value,
    })
    tools.reArrangeChart({ autosize: true })
  }
}

const printContent = (target: string, blob?: Blob) => {
  const fileName = `${formatToSlug(topic)}_topic-trends_${format(new Date(), 'yyyyMMddHHmm')}_(Nodiens).${target}`

  if (target !== 'pdf') {
    FileSaver.saveAs(blob!, fileName)
  } else {
    blobToBase64(blob!, (str) => {
      const pdf = new jsPDF({
        orientation: 'landscape',
      })
      pdf.addImage(str, 'PNG', 10, 10, 280, 110)

      pdf.save(fileName)
    })
  }
}

const print = (target: string) => {
  let previousValue: { from: Time; to: Time } | undefined
  if (chart.value) {
    const opt = chart.value.timeScale().getVisibleRange()
    if (opt) {
      previousValue = {
        from: opt.from,
        to: opt.to,
      }
    }
    chart.value.timeScale().applyOptions({
      visible: true,
    })
    chart.value.applyOptions({
      width: 1584,
      height: 534,
    })
  }
  const canvas = chart.value?.takeScreenshot()
  if (chart.value) {
    chart.value.timeScale().applyOptions({
      visible: false,
    })
    chart.value.applyOptions({
      width: topicChartRef.value?.offsetWidth,
      height: topicChartRef.value?.offsetHeight,
    })
    if (previousValue) {
      chart.value.timeScale().setVisibleRange({
        from: previousValue.from,
        to: previousValue.to,
      })
    }
  }
  if (canvas && exportElement.value) {
    canvas.style.maxWidth = '100%'
    exportElement.value!.style.display = 'block'
    // printTime.value = format(new Date(), 'MMM dd, yyyy kk:mm zzz')
    exportElement.value?.appendChild(canvas)

    const chartWatermark = document.getElementById('chart-watermark')
    if (chartWatermark) {
      const img = chartWatermark.querySelector('img')
      if (img) {
        img.style.left = `${canvas.offsetWidth / 4}px`
        img.style.width = `${canvas.offsetWidth / 2}px`
        img.style.top = `${canvas.offsetHeight / 3.5}px`
      }
    }

    const body = document.querySelector('body')

    if (body) {
      body.style.overflow = 'hidden'
      domtoimage
        .toBlob(exportElement.value)
        .then((blob: any) => {
          printContent(target, blob)
        })
        .finally(() => {
          body.style.overflow = 'unset'
          exportElement.value!.style.display = 'none'
          const canvas = exportElement.value?.querySelector('canvas')
          if (canvas) {
            canvas.remove()
          }
        })
    }
  }
}

watch(darkMode, (value) => {
  tools.setDark(value)
})

watch(error, (newError) => {
  if (newError) {
    toast.add({
      title: 'Error',
      color: 'red',
      description: 'Something went wrong. Please try again later.',
      icon: 'i-heroicons-exclamation-triangle-20-solid',
    })
  }
})

watch([data, topicChartRef, topicChartBottomRef], ([chartData, chartElement, sliderEl]) => {
  if (chartData && chartElement && sliderEl) {
    setTimeout(() => {
      initChart()
    }, 50)
  }
})

watch(isMobile, (value) => {
  if (value) {
    setPriceScaleVisibility('left', false)
  } else {
    setPriceScaleVisibility('left', true)
  }
})

onUnmounted(() => destroyChart())
</script>

<template>
  <div>
    <Head>
      <Title>{{ topicName }} Topic Trends</Title>
    </Head>
    <HeaderPage page-name="topic-trends" />

    <div class="md:px-5 md:py-6">
      <div class="mb-5 hidden items-center justify-between md:flex">
        <UButton
          class="hidden md:inline-flex"
          variant="outline"
          color="black"
          size="md"
          icon="i-heroicons-arrow-left"
          label="Back"
          @click="onBack"
        />
      </div>

      <UCard>
        <div class="mb-4 flex flex-col items-start justify-between gap-4 sm:mb-8 sm:flex-row sm:items-center">
          <div class="border-neutrals-300 rounded border px-2 py-[6px] dark:border-white">
            <p class="text-neutrals-400 text-base dark:text-white">Topic Name: {{ topicName }}</p>
          </div>

          <div
            v-if="isLoading"
            class="flex items-center gap-x-2"
          >
            <USkeleton
              v-for="item in 6"
              :key="item"
              class="h-9 w-12"
            />
          </div>
          <div
            v-else-if="!isChartEmpty"
            class="flex w-full items-center justify-between sm:w-auto sm:justify-start sm:gap-x-2"
          >
            <UButton
              v-for="zoom in zoomViews"
              :key="zoom.id"
              :variant="zoom.active ? 'outline' : 'ghost'"
              :color="zoom.active ? 'brand' : 'black'"
              :class="['px-2 !font-normal sm:px-2.5', { 'dark:!text-neutrals-50 !text-black/50': !zoom.active }]"
              @click="tools.setZoom(zoom.id)"
            >
              {{ zoom.label }}
            </UButton>
            <UButton
              size="md"
              icon="i-material-symbols-share"
              variant="outline"
              color="soft-gray"
              @click="isShareModalOpen = true"
            />
          </div>
        </div>

        <div class="mb-8 flex flex-col justify-between gap-4 md:flex-row md:items-center">
          <div
            v-if="isLoading"
            class="flex items-center gap-x-2"
          >
            <USkeleton
              v-for="item in 4"
              :key="item"
              class="h-9 w-12"
            />
          </div>
          <div
            v-else
            class="flex items-center gap-3"
          >
            <div
              v-for="item in data?.analytics ?? []"
              :key="`topic-chart-${item.platform}-legend`"
              class="border-neutrals-300 flex items-center rounded border px-2 py-[6px]"
            >
              <div
                class="h-2 w-2 rounded-full"
                :style="`background: ${seriesLineStyle[item.platform.toLowerCase()]?.lineColor}`"
              />
              <span class="text-neutrals-300 ml-2 text-base dark:text-white">{{ titleCase(item.platform) }}</span>
            </div>
          </div>

          <div
            v-if="!isChartEmpty"
            class="hidden items-center gap-x-2 self-end sm:flex"
          >
            <USkeleton
              v-if="isLoading"
              class="h-9 w-12"
            />
            <UButton
              v-else
              size="md"
              variant="outline"
              :color="log ? 'brand' : 'soft-gray'"
              class="!font-normal"
              @click="tools.handleLog()"
            >
              LOG
            </UButton>
            <USkeleton
              v-if="isLoading"
              class="h-9 w-12"
            />
            <UPopover v-else>
              <UButton
                size="md"
                color="soft-gray"
                variant="outline"
                icon="i-heroicons-arrow-down-tray"
              />

              <template #panel>
                <div class="flex flex-col p-2">
                  <UButton
                    v-for="item in ['pdf', 'png', 'jpg']"
                    :key="`topic-chart-export-${item}-button`"
                    color="midnight"
                    variant="ghost"
                    block
                    :label="`Export as ${item.toUpperCase()}`"
                    @click="print(item)"
                  />
                </div>
              </template>
            </UPopover>
          </div>
        </div>

        <div
          v-if="isLoading"
          class="grid h-[500px] place-items-center"
        >
          <LottieNodiensLoader />
        </div>
        <CheckFeature
          v-else
          :allowed="!!isFeatureAllowed('sentiment_metrics.topic_trends')"
        >
          <NoDataAvailable
            v-if="isChartEmpty"
            class="h-[300px] md:h-[500px]"
          />
          <div
            v-else
            class="w-full"
          >
            <div
              ref="topicChartRef"
              class="h-[600px] w-full"
            />
            <div class="relative my-2 h-[54px]">
              <div
                id="topic-range-slider"
                class="block bg-transparent"
              />
              <div
                class="absolute top-0 flex h-[50px] w-full justify-center overflow-hidden"
                style="z-index: 0"
              >
                <div
                  ref="topicChartBottomRef"
                  class="h-[50px] w-full overflow-hidden rounded border border-neutral-400"
                  style="max-width: 98.6607%"
                />
              </div>
            </div>
          </div>
        </CheckFeature>
      </UCard>
    </div>

    <div
      ref="exportElement"
      class="pdf h-[693px] w-[1648px] bg-white p-8 dark:bg-black"
      style="display: none"
    >
      <div class="flex items-start justify-between gap-2">
        <div class="flex flex-col gap-2">
          <div class="border-neutrals-300 w-fit rounded border px-2 py-[6px] dark:border-white">
            <p class="text-neutrals-400 text-base dark:text-white">Topic Name: {{ topicName }}</p>
          </div>
          <!-- <div class="flex items-center gap-2 font-normal">
            <div class="text-base">
              {{ topicName ?? '-' }}
            </div>
          </div> -->
          <div>Downloaded from Nodiens, {{ format(new Date(), 'MMM dd, yyyy kk:mm zzz') }}</div>
        </div>

        <div class="flex items-center gap-3">
          <div
            v-for="item in data?.analytics ?? []"
            :key="`topic-chart-export-${item.platform}-legend`"
            class="border-neutrals-300 flex items-center rounded border px-2 py-[6px]"
          >
            <div
              class="h-2 w-2 rounded-full"
              :style="`background: ${seriesLineStyle[item.platform.toLowerCase()]?.lineColor}`"
            />
            <span class="text-neutrals-300 ml-2 text-base dark:text-white">{{ titleCase(item.platform) }}</span>
          </div>
        </div>
      </div>
      <div
        id="chart-watermark"
        class="relative w-full"
      >
        <ColorScheme>
          <img
            v-if="darkMode"
            src="~/assets/media/chart-watermark-night.png"
            class="absolute w-4/12"
            style="left: 33%; top: 50%"
            alt="Chart watermark"
          />
          <img
            v-else
            src="~/assets/media/chart-watermark.png"
            class="absolute w-4/12"
            style="left: 33%; top: 50%"
            alt="Chart watermark"
          />
        </ColorScheme>
      </div>
    </div>

    <LazyShareModal
      v-if="isShareModalOpen"
      :shared-text="fullPath"
      @on-close="isShareModalOpen = false"
    />
  </div>
</template>
<style>
#topic-range-slider {
  margin: auto;
  width: 100%;
  height: 50px;
  background: transparent;
  overflow: hidden;
}
#topic-range-slider .range-slider__thumb:nth-child(3) {
  transform: translate(0%, -50%) !important;
}
#topic-range-slider .range-slider__thumb:nth-child(4) {
  transform: translate(-100%, -50%) !important;
}
.dark #topic-range-slider .range-slider__thumb {
  border: 1px solid#e5e5e5;
}
#topic-range-slider .range-slider__thumb {
  width: 14px;
  height: 100%;
  border-radius: 4px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' fill='%23333' viewBox='0 0 24 24'%3E%3Cpath d='M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z' /%3E%3C/svg%3E")
    #fff;
  border: 1px solid #c3c3c3;
  background-repeat: no-repeat;
  background-position: center;
}
.dark #topic-range-slider .range-slider__range {
  border: 1px solid #d9d9d9;
}
#topic-range-slider .range-slider__range {
  border-radius: 6px;
  background: rgba(0, 163, 255, 0.1);
  border: 1px solid #c3c3c3;
  box-sizing: border-box;
}
</style>
