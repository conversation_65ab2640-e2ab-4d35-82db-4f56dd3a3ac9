<script setup lang="ts">
import FileSaver from 'file-saver'
import jsPDF from 'jspdf'
import domtoimage from 'dom-to-image'
import { format } from 'date-fns'
import { LineStyle } from 'lightweight-charts'

// Constants
import { HTTP_STATUS } from '~/constants/error'
import { CLIMATE_TOOLTIPS } from '~/constants/common-tooltips'
import { ESG_CHART_TYPE_OPTION_LABEL } from '~/constants/options'
import { CLIMATE_METRIC_SLUGS, CARBON_EMISSION_TYPE_OPTIONS } from '~/constants/climate'

// Helpers
import { displayWeight, numericDisplay } from '~/helper/number-helper'
import { formatToSlug } from '~/helper/string-helper'
import { blobToBase64 } from '~/helper/utilities-helper'

// Types
import type { Time } from 'lightweight-charts'
import type { addLineDataType, addRangeAreaDataType } from '~/composables/types/light-chart'
import type { AssetType } from '~/types/appTypes'
import type { CarbonEmissionMetric, EsgAsset } from '~/types'

const { $colorMode, $apiClient } = useNuxtApp()
const config = useRuntimeConfig()
const route = useRoute()
const router = useRouter()
const toast = useToast()
const { onBack } = useBackHandler()
const { isMobile } = useDeviceScreen()

const {
  chart: chartInstance,
  zoomViews,
  log,
  tools,
  addLineSeries,
  addAreaRangeSeries,
  setPriceScaleVisibility,
  renderChart,
  renderBottomChart,
  destroyChart,
} = useLightChart()

const params = route.params as Partial<{ slug: string }>
const query = route.query as Partial<{ metric: string }>

if (!params.slug) {
  throw createError({ statusCode: 404, statusMessage: 'Page Not Found' })
}

const esgChartRef = ref<HTMLElement>()
const esgBottomChartRef = ref<HTMLElement>()
const exportElement = shallowRef<HTMLElement>()

/**
 * Helper number used to temp transfrom small values to larger values to display on lightweight chart
 * This approach is need to show small values, because apparently lightweight chart doesn't support small numbers
 */
const helperNumber = ref(100000)

const isShareModalOpen = ref(false)
const selectedType = ref<string>(CLIMATE_METRIC_SLUGS.carbonEmission)
const selectedMetric = ref(query.metric ?? 'emission')

const selectedAsset = useState<EsgAsset>()
const consensusMechanism = useState<(string | null)[]>(() => [])
const typeEsgLayer = useState(() => '')

const fullPath = computed(() => `${config.public.appUrl}/climate/carbon-emission/${params.slug}`)

const darkMode = computed({
  get() {
    return $colorMode.value === 'dark'
  },
  set(value) {
    if (value) {
      $colorMode.preference = 'dark'
    } else {
      $colorMode.preference = 'light'
    }
  },
})

const { data: chartData, status } = useLazyAsyncData(
  `esg-carbon-emmision-${params.slug}-${selectedMetric.value}`,
  async () => {
    const metrics = {
      emission: 'emission',
      pertx: 'emissionPerTx',
      pertxnode: 'emissionPerTxPerNode',
    } as Record<string, CarbonEmissionMetric>

    const { response, data, error } = await $apiClient.GET('/nodiens/api/v1/esg/carbon-emission/{slug}', {
      params: {
        path: { slug: params.slug! },
        query: { metric: metrics[selectedMetric.value]! },
      },
    })

    if (error) {
      if (response.status === HTTP_STATUS.INTERNAL_SERVER_ERROR) {
        toast.add({
          title: 'Error',
          description: 'Something went wrong. Please try again later.',
          icon: 'i-heroicons-exclamation-triangle-20-solid',
          color: 'red',
        })
      }

      if (response.status === HTTP_STATUS.NOT_FOUND) {
        toast.add({
          title: 'Potential Issue with Data',
          description: 'Asset not found',
          icon: 'i-heroicons-exclamation-triangle',
          color: 'amber',
        })

        setTimeout(() => {
          router.replace('/climate')
        }, 3000)
      }
    }

    return data?.payload || null
  },
  {
    watch: [selectedMetric],
    server: false,
  },
)

const isChartLoading = computed(() => status.value === 'pending')

const dailyMetrics = computed(() => {
  return chartData.value?.dailyMetrics ?? []
})

const analytics = computed(() => {
  return chartData.value?.analytics ?? []
})

const isChartEmpty = computed(() => {
  if (isChartLoading.value) {
    return false
  }

  return dailyMetrics.value.length === 0 && analytics.value.length === 0
})

const typeOptions = computed(() => {
  const isEnConsAvailable = chartData.value?.availabilityOfEnergyConsumption === true
  return [
    {
      label: 'Energy Cons',
      value: CLIMATE_METRIC_SLUGS.energyConsumption,
      disabled: !isEnConsAvailable,
    },
    {
      label: 'CO₂ Emissions',
      value: CLIMATE_METRIC_SLUGS.carbonEmission,
    },
  ]
})

const smallestSelectedAvgUnit = computed(() => {
  if (!chartData.value) {
    return ''
  }

  const averageValues = chartData.value?.analytics.map(([_, value]) => value.bestGuess) ?? []
  const min = Math.min(...averageValues)
  return displayWeight(min).split(' ')[1] + ' CO₂eq'
})

const selectedEmissionSources = computed<Record<string, string>[]>(() => {
  // NOTE: no sources type chart provided on api response.
  // TODO: implement selected sources chart legends.
  return []
})

const initializeChart = () => {
  if (chartInstance.value === undefined) {
    renderChart(
      esgChartRef,
      {
        grid: {
          horzLines: {
            color: '#C3C3C3',
            style: LineStyle.Dashed,
          },
          vertLines: {
            visible: false,
          },
        },
        crosshair: {
          horzLine: {
            visible: false,
            labelVisible: false,
          },
        },
        leftPriceScale: {
          visible: isMobile.value ? false : true,
          borderVisible: false,
          entireTextOnly: true,
          textColor: '#C3C3C3',
          scaleMargins: {
            bottom: 0.01,
          },
        },
        rightPriceScale: {
          visible: isMobile.value ? false : true,
          borderVisible: false,
          entireTextOnly: true,
          textColor: '#C3C3C3',
          scaleMargins: {
            bottom: 0.01,
          },
        },
        timeScale: {
          visible: false,
          borderVisible: false,
          borderColor: 'rgba(197, 203, 206, 1)',
          fixRightEdge: true,
          rightOffset: 5,
          lockVisibleTimeRangeOnResize: true,
        },
        handleScale: {
          pinch: false,
          mouseWheel: false,
          axisPressedMouseMove: false,
        },
        handleScroll: {
          mouseWheel: false,
          vertTouchDrag: false,
          horzTouchDrag: false,
          pressedMouseMove: false,
        },
        ...(chartData.value?.startDate && { startCalculation: format(chartData.value.startDate, 'yyyy-MM-dd') }),
        ...(chartData.value?.endDate && { latestCalculation: format(chartData.value.endDate, 'yyyy-MM-dd') }),
      },
      darkMode.value,
    )
  }

  if (chartInstance.value && chartData.value) {
    if (analytics.value.length > 0) {
      // Add Average line series
      const avgSeries: addLineDataType[] = []
      const series =
        avgSeries.push(
          addLineSeries(ESG_CHART_TYPE_OPTION_LABEL[selectedType.value] || '', {
            priceScaleId: 'left',
            color: '#4FBD6D',
            lineWidth: 2,
            crosshairMarkerVisible: false,
            lastValueVisible: false,
            priceLineVisible: false,
            priceFormat: {
              type: 'custom',
              formatter: (price: number) => {
                return displayWeight(price / helperNumber.value).split(' ')[0]
              },
            },
          }),
        ) - 1

      avgSeries[series]?.setData(
        analytics.value.map(([time, value]) => {
          return {
            time: format(time, 'yyyy-MM-dd'),
            value: value.bestGuess * helperNumber.value,
            customValues: {
              name: 'Best Guess',
            },
          }
        }),
      )

      // Add Range series
      const rangeSeries: addRangeAreaDataType[] = []
      const rangeSerieIndex =
        rangeSeries.push(
          addAreaRangeSeries('range', {
            priceScaleId: 'left',
            lastValueVisible: false,
            priceLineVisible: false,
            closeLineWidth: 1,
            highLineWidth: 1,
            lowLineWidth: 1,
            areaTopColor: 'rgba(79, 189, 109, 0.15)',
            areaBottomColor: 'rgba(79, 189, 109, 0.15)',
            color: 'rgba(79, 189, 109, 0.15)',
            closeLineColor: 'rgba(255, 255, 255, 0)',
            highLineColor: 'rgba(79, 189, 109, 0)',
            lowLineColor: 'rgba(79, 189, 109, 0)',
            priceFormat: {
              type: 'custom',
              formatter: (price: number) => {
                return displayWeight(price / helperNumber.value)
              },
            },
          }),
        ) - 1

      rangeSeries[rangeSerieIndex]?.setData(
        analytics.value.map(([time, value]) => {
          let avg = value.bestGuess

          if (avg < value.lowerBound || avg > value.upperBound) {
            avg = value.lowerBound
          }

          return {
            time: format(time, 'yyyy-MM-dd'),
            low: value.lowerBound * helperNumber.value,
            high: value.upperBound * helperNumber.value,
            close: avg * helperNumber.value,
            customValues: {
              name: 'Range',
            },
          }
        }),
      )
    } else {
      setPriceScaleVisibility('left', false)
    }

    if (dailyMetrics.value.length > 0) {
      // Add TPS line series
      const tpsSeries = addLineSeries('tps', {
        priceScaleId: 'right',
        color: '#ff0000',
        lineWidth: 2,
        crosshairMarkerVisible: false,
        lastValueVisible: false,
        priceLineVisible: false,
        priceFormat: {
          type: 'custom',
          formatter: (price: number) => {
            return numericDisplay(price / helperNumber.value)
          },
        },
      })

      const tpsSeriesData = dailyMetrics.value.map(([time, value]) => {
        return {
          time: format(time, 'yyyy-MM-dd'),
          value: value.throughput * helperNumber.value,
          customValues: {
            name: 'Throughput',
          },
        }
      })

      tpsSeries?.setData(tpsSeriesData)
    } else {
      setPriceScaleVisibility('right', false)
    }

    tools.reArrangeChart({ autosize: true })
    tools.prepareTooltip({
      topOffset: -30,
      darkMode: darkMode.value,
      valueFormatter: (value, seriesKey) => {
        return seriesKey === 'tps'
          ? `${numericDisplay(value / helperNumber.value)} tps`
          : displayWeight(value / helperNumber.value)
      },
      customLineCallback: (pointX) => {
        const value = dailyMetrics.value[pointX]?.[1]?.validatorCount

        return {
          unit: '#',
          label: 'Validators',
          value: value ? numericDisplay(value, 0) : '',
        }
      },
    })
    renderBottomChart(esgBottomChartRef, '#range-slider', darkMode.value)
  }
}

const searchAsset = async (q: string) => {
  return await $apiClient
    .GET('/nodiens/api/v1/esg/carbon-emission/asset', {
      params: {
        query: {
          ...(q && { terms: q }),
        },
      },
    })
    .then((res) => res.data?.payload?.data ?? [])
}

const esgChartTypeOptionLabel = computed(() => ESG_CHART_TYPE_OPTION_LABEL[selectedMetric.value])

const printContent = (target: string, blob?: Blob) => {
  const fileName = `${params.slug}_${formatToSlug(esgChartTypeOptionLabel.value || 'default')}_${format(new Date(), 'yyyyMMddHHmm')}_(Nodiens)`
  if (target !== 'pdf') {
    FileSaver.saveAs(blob!, `${fileName}.${target}`)
  } else {
    blobToBase64(blob!, (str) => {
      const pdf = new jsPDF({
        orientation: 'landscape',
      })
      pdf.addImage(str, 'PNG', 10, 10, 280, 110)
      pdf.save(`${fileName}.${target}`)
    })
  }
}

const print = (target: 'png' | 'jpg' | 'pdf') => {
  let previousValue: { from: Time; to: Time } | undefined
  if (chartInstance.value) {
    const opt = chartInstance.value.timeScale().getVisibleRange()
    if (opt) {
      previousValue = {
        from: opt.from,
        to: opt.to,
      }
    }
    chartInstance.value.timeScale().applyOptions({
      visible: true,
    })
    chartInstance.value.applyOptions({
      width: 1584,
      height: 534,
    })
  }
  const canvas = chartInstance.value?.takeScreenshot()
  if (chartInstance.value) {
    chartInstance.value.timeScale().applyOptions({
      visible: false,
    })
    chartInstance.value.applyOptions({
      width: esgChartRef.value?.offsetWidth,
      height: esgChartRef.value?.offsetHeight,
    })
    if (previousValue) {
      chartInstance.value.timeScale().setVisibleRange({
        from: previousValue.from,
        to: previousValue.to,
      })
    }
  }
  if (canvas && exportElement.value) {
    canvas.style.maxWidth = '100%'
    exportElement.value!.style.display = 'block'
    exportElement.value?.appendChild(canvas)

    const chartWatermark = document.getElementById('chart-watermark')
    if (chartWatermark) {
      const img = chartWatermark.querySelector('img')
      if (img) {
        img.style.left = `${canvas.offsetWidth / 4}px`
        img.style.width = `${canvas.offsetWidth / 2}px`
        img.style.top = `${canvas.offsetHeight / 3.5}px`
      }
    }

    const body = document.querySelector('body')

    if (body) {
      body.style.overflow = 'hidden'
      domtoimage
        .toBlob(exportElement.value)
        .then((blob: any) => {
          printContent(target, blob)
        })
        .finally(() => {
          body.style.overflow = 'unset'
          exportElement.value!.style.display = 'none'
          const canvas = exportElement.value?.querySelector('canvas')
          if (canvas) {
            canvas.remove()
          }
        })
    }
  }
}

const handleAssetChange = (asset: AssetType) => {
  router.push(`/climate/carbon-emission/${asset?.slug}`)
}

watch(isMobile, (value) => {
  if (value) {
    setPriceScaleVisibility('left', false)
    setPriceScaleVisibility('right', false)
  } else {
    setPriceScaleVisibility('left', true)
    setPriceScaleVisibility('right', true)
  }
})

watch(darkMode, (value) => {
  tools.setDark(value)
})

// Initiate selected asset, consensus mechanism, and type esg layer
watch(chartData, (newChartData) => {
  if (newChartData) {
    selectedAsset.value = {
      slug: newChartData.feeds.slug,
      symbol: newChartData.feeds.symbol,
      name: newChartData.feeds.name,
      logo: newChartData.feeds.logo,
    }

    consensusMechanism.value = [newChartData.consensus]

    if (!newChartData?.layer) {
      typeEsgLayer.value = 'L' + newChartData.layer
    } else {
      typeEsgLayer.value = ''
    }
  }
})

watch([chartData, esgChartRef, esgBottomChartRef], ([newChartData, chartElement, sliderEl]) => {
  if (newChartData && chartElement && sliderEl) {
    setTimeout(() => {
      initializeChart()
    }, 50)
  }
})

watch(selectedType, (type) => {
  if (type) {
    destroyChart()

    navigateTo(`/climate/${type}/${params.slug}`)
  }
})

watch(selectedMetric, (metric) => {
  if (chartInstance.value) {
    destroyChart()
  }
  router.replace({
    query: { metric },
  })
})

onUnmounted(() => {
  destroyChart()
})
</script>

<template>
  <div>
    <Head>
      <Title>{{ selectedAsset?.name }} Climate Indexes</Title>
    </Head>
    <HeaderPage page-name="climate-indices" />
    <div class="md:px-5 md:py-6">
      <div class="mb-4 hidden justify-between gap-2 md:mb-6 md:flex">
        <UButton
          class="inline-flex"
          variant="outline"
          color="black"
          size="md"
          icon="i-heroicons-arrow-left"
          label="Back"
          @click="onBack"
        />

        <div class="flex gap-2">
          <div
            v-if="typeEsgLayer"
            class="border-neutrals-300 flex items-center gap-2 rounded border px-3 py-2 dark:border-white"
          >
            <p class="text-neutrals-400 text-base dark:text-white">
              {{ typeEsgLayer }}
            </p>
            <InformationPopover class="mt-1">
              <TooltipContent>
                <p v-html="CLIMATE_TOOLTIPS.esgLayer" />
              </TooltipContent>
            </InformationPopover>
          </div>
          <div
            v-for="item in consensusMechanism"
            :key="(item as string).replace(' ', '-')"
            class="border-neutrals-300 flex items-center gap-2 rounded border px-3 py-2 dark:border-white"
          >
            <p class="text-neutrals-400 text-base dark:text-white">
              {{ item }}
            </p>
            <InformationPopover class="mt-1">
              <TooltipContent>
                <p v-html="CLIMATE_TOOLTIPS.consensusMechanism" />
              </TooltipContent>
            </InformationPopover>
          </div>
        </div>
      </div>

      <UCard>
        <div class="flex flex-col gap-4">
          <div class="flex flex-col gap-4">
            <div class="flex gap-2 self-end md:hidden">
              <div
                v-if="typeEsgLayer"
                class="border-neutrals-300 flex items-center gap-2 rounded border px-3 py-2 dark:border-white"
              >
                <p class="text-neutrals-400 text-base dark:text-white">
                  {{ typeEsgLayer }}
                </p>
                <InformationPopover class="mt-1">
                  <TooltipContent>
                    <p v-html="CLIMATE_TOOLTIPS.esgLayer" />
                  </TooltipContent>
                </InformationPopover>
              </div>
              <div
                v-for="item in consensusMechanism"
                :key="(item as string).replace(' ', '_')"
                class="border-neutrals-300 flex items-center gap-2 rounded border px-3 py-2 dark:border-white"
              >
                <p class="text-neutrals-400 text-base dark:text-white">
                  {{ item }}
                </p>
                <InformationPopover class="mt-1">
                  <TooltipContent>
                    <p v-html="CLIMATE_TOOLTIPS.consensusMechanism" />
                  </TooltipContent>
                </InformationPopover>
              </div>
            </div>
            <div class="flex flex-col flex-wrap gap-4 md:flex-row md:justify-between">
              <div>
                <div
                  v-if="isChartLoading"
                  class="flex gap-2"
                >
                  <USkeleton class="h-10 flex-1 md:w-44" />
                  <USkeleton class="h-10 flex-1 md:w-44" />
                  <USkeleton class="h-10 flex-1 md:w-44" />
                </div>
                <div
                  v-else
                  class="flex flex-col gap-4 md:flex-row"
                >
                  <AssetSelector
                    v-model="selectedAsset"
                    :search-assets="searchAsset"
                    @change="handleAssetChange"
                  />

                  <USelectMenu
                    v-model="selectedType"
                    :options="typeOptions"
                    value-attribute="value"
                    variant="solid"
                    size="md"
                    color="black"
                    class="w-full md:w-fit"
                    :popper="{ placement: 'bottom-start' }"
                    :ui-menu="{
                      container: 'w-min',
                      label: 'text-base',
                    }"
                  />

                  <USelectMenu
                    v-model="selectedMetric"
                    :options="CARBON_EMISSION_TYPE_OPTIONS"
                    value-attribute="value"
                    variant="solid"
                    color="black"
                    size="md"
                    class="w-full md:w-fit"
                    :popper="{ placement: 'bottom-start' }"
                    :ui-menu="{
                      container: 'w-min',
                      label: 'text-base',
                    }"
                  />
                </div>
              </div>

              <div class="md:ml-auto lg:ml-0">
                <div
                  v-if="isChartLoading"
                  class="flex items-center gap-x-2"
                >
                  <USkeleton
                    v-for="item in 6"
                    :key="item"
                    class="h-9 w-12"
                  />
                </div>
                <div
                  v-else
                  class="flex items-center justify-between gap-1.5 md:gap-2"
                >
                  <UButton
                    v-for="zoom in zoomViews"
                    :key="zoom.id"
                    :variant="zoom.active ? 'outline' : 'ghost'"
                    :color="zoom.active ? 'brand' : 'black'"
                    :class="[
                      'px-1.5 !font-normal sm:px-2.5',
                      { 'dark:!text-neutrals-50 !text-black/50': !zoom.active },
                    ]"
                    @click="tools.setZoom(zoom.id)"
                  >
                    {{ zoom.label }}
                  </UButton>
                  <UButton
                    size="md"
                    icon="i-material-symbols-share"
                    color="soft-gray"
                    variant="outline"
                    @click="isShareModalOpen = true"
                  />
                </div>
              </div>
            </div>
            <div class="flex justify-between">
              <div>
                <div
                  v-if="isChartLoading"
                  class="flex gap-2"
                >
                  <USkeleton class="h-10 w-44" />
                  <USkeleton class="h-10 w-16" />
                </div>
                <div
                  v-else-if="selectedEmissionSources.length"
                  class="flex flex-wrap items-center gap-3"
                >
                  <div
                    v-for="item in selectedEmissionSources"
                    :key="String(item.label)"
                    class="border-neutrals-300 flex items-center rounded border px-2 py-[6px] dark:border-white"
                  >
                    <div
                      :style="{ backgroundColor: item.color }"
                      class="h-2 w-2 rounded-full"
                    />
                    <span class="text-neutrals-300 ml-2 text-base capitalize dark:text-white">{{ item.label }}</span>
                  </div>
                </div>
                <div
                  v-else
                  class="flex items-center gap-3"
                >
                  <div class="border-neutrals-300 flex items-center rounded border px-2 py-[6px] dark:border-white">
                    <div class="bg-accent-green-400 h-2 w-2 rounded-full" />
                    <span class="text-neutrals-300 ml-2 text-base capitalize dark:text-white">{{
                      esgChartTypeOptionLabel
                    }}</span>
                  </div>
                  <div class="border-neutrals-300 flex items-center rounded border px-2 py-[6px] dark:border-white">
                    <div class="bg-accent-red-700 h-2 w-2 rounded-full" />
                    <span class="text-neutrals-300 ml-2 text-base dark:text-white">Throughput</span>
                  </div>
                </div>
              </div>

              <div class="hidden md:block">
                <div
                  v-if="isChartLoading"
                  class="flex items-center gap-x-2"
                >
                  <USkeleton
                    v-for="item in 2"
                    :key="item"
                    class="h-9 w-12"
                  />
                </div>

                <div
                  v-else
                  class="flex items-center gap-x-2 self-end"
                >
                  <UButton
                    size="md"
                    variant="outline"
                    :color="log ? 'brand' : 'soft-gray'"
                    class="!font-normal"
                    @click="tools.handleLog"
                  >
                    LOG
                  </UButton>
                  <UPopover>
                    <UButton
                      size="md"
                      color="soft-gray"
                      variant="outline"
                      icon="i-heroicons-arrow-down-tray"
                    />

                    <template #panel="{ close }">
                      <div class="flex flex-col p-2">
                        <UButton
                          color="black"
                          variant="ghost"
                          block
                          @click="
                            () => {
                              close()
                              print('pdf')
                            }
                          "
                        >
                          Export as PDF
                        </UButton>
                        <UButton
                          color="black"
                          variant="ghost"
                          block
                          @click="
                            () => {
                              close()
                              print('png')
                            }
                          "
                        >
                          Export as PNG
                        </UButton>
                        <UButton
                          color="black"
                          variant="ghost"
                          block
                          @click="
                            () => {
                              close()
                              print('jpg')
                            }
                          "
                        >
                          Export as JPG
                        </UButton>
                      </div>
                    </template>
                  </UPopover>
                </div>
              </div>
            </div>
          </div>
          <div
            v-if="isChartLoading"
            class="v1-4xl:h-[600px] grid h-[400px] w-full place-items-center xl:h-[500px]"
          >
            <LottieNodiensLoader />
          </div>
          <div v-else>
            <NoDataAvailable
              v-if="isChartEmpty"
              class="v1-4xl:h-[600px] h-[400px] xl:h-[500px]"
            />
            <div v-else>
              <div
                v-if="!isMobile"
                class="flex w-full items-center px-7 py-1"
              >
                <p
                  v-if="analytics.length"
                  class="text-neutrals-300 text-right text-xs font-medium"
                >
                  {{ smallestSelectedAvgUnit }}
                </p>
                <p
                  v-if="!selectedEmissionSources.length && dailyMetrics.length"
                  class="text-neutrals-300 ml-auto text-xs font-medium"
                >
                  tps
                </p>
              </div>
              <ClientOnly>
                <div
                  ref="esgChartRef"
                  class="v1-4xl:h-[600px] h-[400px] w-full xl:h-[500px]"
                />
              </ClientOnly>
              <div class="relative my-2 h-fit">
                <div
                  id="range-slider"
                  class="block bg-transparent"
                />
                <div
                  class="absolute top-0 flex h-[50px] w-full justify-center overflow-hidden"
                  style="z-index: 0"
                >
                  <div
                    ref="esgBottomChartRef"
                    class="h-[50px] w-full overflow-hidden rounded border border-neutral-400"
                    style="max-width: 98.6607%"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <div
      ref="exportElement"
      class="min-h-[693px] w-[1648px] bg-white p-8 dark:bg-black"
      style="display: none"
    >
      <div class="flex justify-between gap-2">
        <div class="flex flex-col gap-2">
          <div class="flex items-center gap-2 font-normal">
            <img
              :src="
                chartData?.feeds?.logo ? '/api/v2/prx-image?url=' + chartData?.feeds?.logo + '&responseType=blob' : ''
              "
              alt="logo"
              class="mr-3 h-6 w-6"
            />
            <div class="text-base">
              {{ chartData?.feeds?.name ?? '-' }}
              <span class="text-neutrals-300">({{ chartData?.feeds?.symbol?.toUpperCase() ?? '-' }})</span>
            </div>
          </div>
          <div>Downloaded from Nodiens, {{ format(new Date(), 'MMM dd, yyyy kk:mm zzz') }}</div>
        </div>

        <div
          v-if="selectedEmissionSources.length"
          class="flex flex-wrap items-center gap-3"
        >
          <div
            v-for="item in selectedEmissionSources"
            :key="String(item.label)"
            class="border-neutrals-300 flex items-center rounded border px-2 py-[6px] dark:border-white"
          >
            <div
              :style="{ backgroundColor: item.color }"
              class="h-2 w-2 rounded-full"
            />
            <span class="text-neutrals-300 ml-2 text-base capitalize dark:text-white">{{ item.label }}</span>
          </div>
        </div>

        <div
          v-else
          class="flex items-center gap-3"
        >
          <div class="border-neutrals flex items-center rounded border px-2 py-[6px]">
            <div class="bg-accent-green-400 h-2 w-2 rounded-full" />
            <span class="text-neutrals-300 ml-2 text-base capitalize dark:text-white">{{
              esgChartTypeOptionLabel
            }}</span>
          </div>
          <div class="border-neutrals-300 flex items-center rounded border px-2 py-[6px]">
            <div class="bg-accent-red-700 h-2 w-2 rounded-full" />
            <span class="text-neutrals-300 ml-2 text-base dark:text-white">Throughput</span>
          </div>
        </div>
      </div>

      <div class="mb-1 mt-6 flex w-full items-center justify-between px-7">
        <p class="text-neutrals-300 text-right text-xs font-medium">
          {{ smallestSelectedAvgUnit }}
        </p>
        <p
          v-if="!selectedEmissionSources.length"
          class="text-neutrals-300 text-xs font-medium"
        >
          tps
        </p>
      </div>

      <div
        id="chart-watermark"
        class="relative w-full"
      >
        <ColorScheme>
          <img
            v-if="darkMode"
            src="~/assets/media/chart-watermark-night.png"
            class="absolute w-4/12"
            style="left: 33%; top: 50%"
            alt="Chart watermark"
          />
          <img
            v-else
            src="~/assets/media/chart-watermark.png"
            class="absolute w-4/12"
            style="left: 33%; top: 50%"
            alt="Chart watermark"
          />
        </ColorScheme>
      </div>
    </div>

    <LazyShareModal
      v-if="isShareModalOpen"
      :shared-text="fullPath"
      @on-close="isShareModalOpen = false"
    />
  </div>
</template>

<style>
#range-slider {
  margin: auto;
  width: 100%;
  height: 50px;
  background: transparent;
  overflow: hidden;
}
#range-slider .range-slider__thumb:nth-child(3) {
  transform: translate(0%, -50%) !important;
}
#range-slider .range-slider__thumb:nth-child(4) {
  transform: translate(-100%, -50%) !important;
}
.dark #range-slider .range-slider__thumb {
  border: 1px solid#e5e5e5;
}
#range-slider .range-slider__thumb {
  width: 14px;
  height: 100%;
  border-radius: 4px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='18' fill='%23333' viewBox='0 0 24 24'%3E%3Cpath d='M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z' /%3E%3C/svg%3E")
    #fff;
  border: 1px solid #c3c3c3;
  background-repeat: no-repeat;
  background-position: center;
}
.dark #range-slider .range-slider__range {
  border: 1px solid #d9d9d9;
}
#range-slider .range-slider__range {
  border-radius: 6px;
  background: rgba(0, 163, 255, 0.1);
  border: 1px solid #c3c3c3;
  box-sizing: border-box;
}
</style>
