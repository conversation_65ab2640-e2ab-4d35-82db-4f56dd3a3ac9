<script setup lang="ts">
import { STABLECOIN_LINE_STYLE } from '~/constants/stablecoin'

// Types
import {
  dexCompareKeyMetrics,
  dexMetricColor,
  dexMetricGroups,
  dexMetrics,
  getDexMetricObjKey,
} from '~/constants/metrics/decentralisation-indices'
import { useRadarLineChart } from '~/composables/useRadarLineChart'
import type { v2AssetCoin } from '~/server/trpc/trpc'
import type { compareUnitType } from '~/components/organism/DecentralisationLineChart/types'

provide('coinApi', 'v2.user.decentralize.asset')
provide('coinFilterLabel', 'project')
provide('queryKey', 'project')

const { $client } = useNuxtApp()
const config = useRuntimeConfig()
const route = useRoute()
const { onBack } = useBackHandler()

const isShareModalOpen = ref(false)

// TODO: Remove once API has properly handled data issues.
const showNoLineData = ref(false)

provide('showNoLineData', showNoLineData)

const fullPath = computed(() => {
  const queryParams = new URLSearchParams(route.query as Record<string, string>)
  return `${config.public.appUrl}/dex?${queryParams}`
})

const { selectedLabelIndex, stablecoinLinechartKey, coin1Filter, coin2Filter, coin3Filter, lineChartTitle } =
  useRadarLineChart()

await useAsyncData(async () => {
  const data = await $client.v2.user.decentralize.asset.query()
  if (!route.query.project1) {
    coin1Filter.value.coin = data.assets[0] as unknown as v2AssetCoin
    coin2Filter.value.coin = data.assets[1] as unknown as v2AssetCoin
    coin3Filter.value.coin = data.assets[2] as unknown as v2AssetCoin
  } else {
    coin1Filter.value.coin =
      (data.assets.find((asset) => asset.slug === route.query.project1) as unknown as v2AssetCoin) ?? undefined
    coin2Filter.value.coin =
      (data.assets.find((asset) => asset.slug === route.query.project2) as unknown as v2AssetCoin) ?? undefined
    coin3Filter.value.coin =
      (data.assets.find((asset) => asset.slug === route.query.project3) as unknown as v2AssetCoin) ?? undefined
  }

  // TODO: This causes a slight delay on changing the selected label in radar chart. Must move somewhere else to remove the delay.
  if (route.query.metric) {
    const selectedMetrics = dexCompareKeyMetrics.findIndex((d: any) => d === route.query.metric)
    selectedLabelIndex.value = selectedMetrics
    lineChartTitle.value = getDexMetricObjKey('label', selectedMetrics) as string
  } else {
    selectedLabelIndex.value = 0
    lineChartTitle.value = getDexMetricObjKey('label', 0) as string
  }
})

const coinsQuery = computed(() => {
  const coins: string[] = []

  if (route.query.project1) {
    coins.push(route.query.project1 as string)
  }
  if (route.query.project2) {
    coins.push(route.query.project2 as string)
  }
  if (route.query.project3) {
    coins.push(route.query.project3 as string)
  }

  return coins
})

const {
  data: lineChartData,
  status: lineChartStatus,
  refresh: refreshLineChart,
} = await useLazyAsyncData(
  'getLineChart',
  async () => {
    try {
      if (!coinsQuery.value.length) {
        return null
      }

      const data = await $client.v2.user.decentralize.comparison.query({
        slugs: coinsQuery.value,
        compareUnit: !getDexMetricObjKey('multipleCompareUnit', selectedLabelIndex.value)
          ? (getDexMetricObjKey('compareUnit', selectedLabelIndex.value) as compareUnitType)
          : (getDexMetricObjKey('compareUnit', selectedLabelIndex.value) as compareUnitType),
      })

      stablecoinLinechartKey.value += 1
      showNoLineData.value = false
      return data
    } catch (e) {
      showNoLineData.value = true
      console.error(e)
      return null
    }
  },
  {
    watch: [() => route.query.project1, () => route.query.project2, () => route.query.project3, selectedLabelIndex],
    immediate: false,
    server: false,
  },
)

const {
  data: spiderData,
  status: radarStatus,
  refresh: refreshSpiderData,
} = await useLazyAsyncData(
  'getSpiderData',
  async () => {
    if (coinsQuery.value.length) {
      return await $client.v2.user.decentralize.chart.query({
        slugs: coinsQuery.value,
      })
    }
    return null
  },
  {
    watch: [() => route.query.project1, () => route.query.project2, () => route.query.project3],
    immediate: false,
    server: false,
  },
)

const radarPending = computed(() => radarStatus.value === 'pending')

const linePending = computed(() => lineChartStatus.value === 'pending')

const radarDataset = computed(() => {
  return {
    labels: dexMetrics.map((label) => {
      const parts = label.split(' ')
      const firstPart = parts.slice(0, 2).join(' ')
      const secondPart = parts.slice(2).join(' ')
      return [firstPart, secondPart]
    }),
    datasets: coinsQuery.value.map((coin, coinIndex) => {
      const coinColor = []

      if (route.query.project1) {
        coinColor.push(STABLECOIN_LINE_STYLE['coin-1'].color)
      }
      if (route.query.project2) {
        coinColor.push(STABLECOIN_LINE_STYLE['coin-2'].color)
      }
      if (route.query.project3) {
        coinColor.push(STABLECOIN_LINE_STYLE['coin-3'].color)
      }

      const coinData = spiderData.value?.find((data) => data.slug === coin)

      return {
        label: coin,
        borderColor: coinColor[coinIndex],
        fill: false,
        data: [
          Number(coinData?.metrics.ipAuthDistrGini ?? 0),
          Number(coinData?.metrics.ipParticipantDivGini ?? 0),
          Number(coinData?.metrics.ipAuthorInflConcHHI ?? 0),
          Number(coinData?.metrics.ipGovOrdinal ?? 0), // red limit
          Number(coinData?.metrics.rcpDevDistrGini ?? 0),
          Number(coinData?.metrics.rcpParticipantDivShannon ?? 0),
          Number(coinData?.metrics.rcpDevInflConcHHI ?? 0),
          Number(coinData?.metrics.rcdRevrPowerConcHHI ?? 0), // green limit
          Number(coinData?.metrics.consensusPowerNeGini ?? 0),
          Number(coinData?.metrics.consensusPowerNeTheil ?? 0),
          Number(coinData?.metrics.consensusPowerConcNakamoto ?? 0),
          Number(coinData?.metrics.consensusPowerConcHHI ?? 0), // purple limit
          Number(coinData?.metrics.coinDistrOrdinal ?? 0),
        ],
        borderWidth: 1.5,
        hidden: false,
      }
    }),
  }
})

const titlePage = computed(() => {
  const activeCoinFilter = []
  if (coin1Filter.value.coin) {
    activeCoinFilter.push(coin1Filter.value.coin.name)
  }
  if (coin2Filter.value.coin) {
    activeCoinFilter.push(coin2Filter.value.coin.name)
  }
  if (coin3Filter.value.coin) {
    activeCoinFilter.push(coin3Filter.value.coin.name)
  }
  if (activeCoinFilter.length === 1) {
    return activeCoinFilter[0]
  }
  if (activeCoinFilter.length === 2) {
    return `${activeCoinFilter[0]} and ${activeCoinFilter[1]}`
  }
  if (activeCoinFilter.length === 3 && activeCoinFilter.every((x) => !!x)) {
    return `${activeCoinFilter[0]}, ${activeCoinFilter[1]}, and ${activeCoinFilter[2]}`
  }
  return ''
})

onMounted(async () => {
  await Promise.all([refreshLineChart(), refreshSpiderData()])
})
</script>
<template>
  <div>
    <Head>
      <Title> {{ titlePage }} Decentralization Indexes</Title>
    </Head>
    <HeaderPage
      page-name="decentralisation-indices"
      faq-slug="decentralisation-indices"
    />

    <div class="md:px-5 md:py-6">
      <UButton
        class="hidden md:inline-flex"
        variant="outline"
        color="black"
        size="md"
        icon="i-heroicons-arrow-left"
        label="Back"
        @click="onBack"
      />

      <RadarLineChart
        :radar-dataset="radarDataset"
        :radar-loading="radarPending"
        :line-loading="linePending"
        :line-chart-data="lineChartData"
        :metric-color="dexMetricColor"
        :metric-obj-key="getDexMetricObjKey"
        :metric-groups="dexMetricGroups"
        hide-value-unit
        is-decentralisation
      />
    </div>

    <LazyShareModal
      v-if="isShareModalOpen"
      :shared-text="fullPath"
      @on-close="isShareModalOpen = false"
    />
  </div>
</template>
