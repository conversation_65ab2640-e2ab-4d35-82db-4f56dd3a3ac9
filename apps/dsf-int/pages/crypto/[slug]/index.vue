<script setup lang="ts">
import { format, subYears } from 'date-fns'

// Constants
import { LEGEND_MOOD_TRUST, LEGEND_PLATFORMS, LEGEND_PRICE_VOLUME } from '~/constants/legends'
import { ONBOARDING_CRYPTO_SUMMARY_PAGE, ONBOARDING_PATHS } from '~/constants/onboarding'
import { ASSET_CRYPTO_TOOLTIPS } from '~/constants/common-tooltips'

// Helpers
import { numericDisplay, displayWattage } from '~/helper/number-helper'
import { titleCase } from '~/helper/string-helper'

// Types
import type { AssetType } from '~/types/appTypes'
import type { v2AssetList } from '~/server/trpc/trpc'
import type { OverviewChartDataset } from '~/components/molecule/OverviewChart/OverviewChart.vue'

const { $apiClient } = useNuxtApp()
const toast = useToast()
const router = useRouter()
const route = useRoute()

const params = route.params as Partial<{ slug: string }>

const maxVolumeChart = ref(0)
const lastClickTime = ref(0)

const selectedAsset = useState<v2AssetList['data'][0] | undefined>()
const isScrolling = ref(false)
let scrollTimeout: NodeJS.Timeout | null = null
const { isFeatureAllowed } = useFeature()

const startDate = format(subYears(new Date(), 1), 'yyyy-MM-dd')
const endDate = format(new Date(), 'yyyy-MM-dd')

const { data: onboardingData, status: onboardingStatus } = await useLazyAsyncData(
  'tokenCryptoOverviewOnboarding',
  async () => {
    const { data } = await $apiClient.GET('/nodiens/api/v1/account/onboarding', {
      params: {
        query: {
          path: ONBOARDING_PATHS.CRYPTO_SUMMARY,
        },
      },
    })

    return data?.payload.isChecked || false
  },
  {
    server: false,
  },
)

const { data: overviewData, status: overviewStatus } = useLazyAsyncData(
  `overview.crypto.${params.slug}`,
  async () => {
    if (!params.slug) {
      toast.add({
        title: 'Invalid Request',
        description: 'The requested asset is not specified. Redirecting to the homepage.',
        icon: 'i-heroicons-exclamation-triangle',
        color: 'amber',
      })
      setTimeout(() => {
        navigateTo('/', { replace: true })
      }, 3000)
      throw new Error('Slug is undefined')
    }

    try {
      const { data } = await $apiClient.GET('/nodiens/api/v1/asset/{type}/{slug}', {
        params: {
          path: {
            slug: params.slug,
            type: 'crypto',
          },
        },
      })

      if (data?.code !== 'success') {
        throw new Error(data?.code || 'UNKNOWN_ERROR')
      }

      return data?.payload
    } catch (error: any) {
      const isErrorAssetNotFound = error.message === 'ASSET_NOT_FOUND'

      toast.add({
        title: isErrorAssetNotFound ? 'Potential Issue with Data' : 'Error',
        description: isErrorAssetNotFound ? 'Asset not found' : 'Something went wrong. Please try again later.',
        color: isErrorAssetNotFound ? 'amber' : 'red',
        icon: 'i-heroicons-exclamation-triangle',
      })

      if (isErrorAssetNotFound) {
        setTimeout(() => {
          navigateTo('/', { replace: true })
        }, 3000)
      }

      return null
    }
  },
  {
    server: false,
  },
)

watch(overviewData, (newOverviewData) => {
  // Initiate selected asset
  if (newOverviewData) {
    const { name, type, symbol } = newOverviewData
    selectedAsset.value = {
      slug: params.slug!,
      logo: newOverviewData.logo ?? '',
      name,
      type,
      symbol,
    }
  }
})

const isOverviewLoading = computed(() => {
  return onboardingStatus.value === 'pending' || overviewStatus.value === 'pending'
})

const lastCalculationDate = computed(() => {
  return overviewData.value?.latestCalculation ? format(overviewData.value.latestCalculation, 'MMM dd, yyyy') : '-'
})

const { data: chartDataMoodTrust, status: chartDataMoodTrustStatus } = useLazyAsyncData(
  `moodTrustIndex.${params.slug}`,
  async () => {
    if (!params.slug) return []

    const apiParams = {
      params: {
        path: { slug: params.slug },
        query: { layer1: true, startDate, endDate },
      },
    }

    const [moodRes, trustRes] = await Promise.allSettled([
      $apiClient.GET('/nodiens/api/v1/mood-index/{slug}', apiParams),
      $apiClient.GET('/nodiens/api/v1/trust-index/{slug}', apiParams),
    ])

    if (moodRes.status !== 'fulfilled' || trustRes.status !== 'fulfilled') return []

    const moodSeries =
      moodRes.value?.data?.payload?.layer1?.map(([time, value]) => ({
        time: format(new Date(time), 'yyyy-MM-dd'),
        value,
      })) ?? []

    const trustSeries =
      trustRes.value?.data?.payload?.layer1?.map(([time, value]) => ({
        time: format(new Date(time), 'yyyy-MM-dd'),
        value,
      })) ?? []

    if (moodSeries.length === 0 && trustSeries.length === 0) return []

    return [
      {
        type: 'line',
        name: 'Mood',
        color: 'rgba(0,163,255,1)',
        data: moodSeries,
        showLastMarker: true,
        markerBackgroundColor: 'rgba(0,163,255,1)',
        markerShadowColor: 'rgba(0,163,255,0.35)',
      },
      {
        type: 'line',
        name: 'Trust',
        color: 'rgba(79,189,109,1)',
        data: trustSeries,
        showLastMarker: true,
        markerBackgroundColor: 'rgba(79,189,109,1)',
        markerShadowColor: 'rgba(79,189,109,0.35)',
      },
    ] satisfies OverviewChartDataset[]
  },
  {
    server: false,
  },
)

const { data: chartDataPriceVolume, status: priceVolumeStatus } = useLazyAsyncData(
  `priceVolume.${params.slug}`,
  async () => {
    if (!params.slug) return {}

    const [priceRes, volumeRes] = await Promise.allSettled([
      $apiClient.GET('/nodiens/api/v1/financial/history/{slug}', {
        params: {
          path: { slug: params.slug },
          query: { startDate, endDate, metric: 'price' },
        },
      }),

      $apiClient.GET('/nodiens/api/v1/price-market/volume-history/{slug}', {
        params: {
          path: { slug: params.slug },
          query: { startDate, endDate },
        },
      }),
    ])

    if (priceRes.status !== 'fulfilled' || volumeRes.status !== 'fulfilled') return {}

    const priceSeries =
      priceRes.value?.data?.payload?.entries.map(([time, value]) => ({
        time: format(new Date(time), 'yyyy-MM-dd'),
        value,
      })) ?? []

    const volumeSeries =
      volumeRes.value?.data?.payload?.entries.map(([time, value]) => ({
        time: format(new Date(time), 'yyyy-MM-dd'),
        value,
      })) ?? []

    return {
      price: [
        {
          type: 'area',
          name: 'Price',
          color: '#FFA500',
          data: priceSeries,
          showLastMarker: true,
          markerBackgroundColor: 'rgba(255,165,0,1)',
          markerShadowColor: 'rgba(255,165,0,0.35)',
          areaTopColor: 'rgba(255,165,0,0.50)',
          areaBottomColor: 'rgba(255,165,0,0.20)',
        },
      ],
      volume: [
        {
          type: 'histogram',
          name: 'Volume',
          color: '#C3C3C3',
          data: volumeSeries,
          showLastMarker: true,
        },
      ],
    } satisfies Record<string, OverviewChartDataset[]>
  },
  {
    server: false,
  },
)

watch(priceVolumeStatus, (newStatus) => {
  if (newStatus === 'success') {
    maxVolumeChart.value = Math.max(...(chartDataPriceVolume.value?.volume?.[0]?.data?.map((d) => d.value ?? 0) ?? [0]))
  }
})

const isChartDataPriceVolumeEmpty = computed(() => {
  return (
    (chartDataPriceVolume.value?.price?.[0]?.data?.length ?? 0) === 0 &&
    (chartDataPriceVolume.value?.volume?.[0]?.data?.length ?? 0) === 0
  )
})

const { data: chartDataMessageRaw, status: chartMessageStatus } = useLazyAsyncData(
  `communityMessage.${params.slug}`,
  async () => {
    if (!params.slug) return null

    const response = await $apiClient.GET('/nodiens/api/v1/community/history/{metric}/{slug}', {
      params: {
        path: { slug: params.slug!, metric: 'MESSAGE_COUNT' },
        query: { startDate, endDate, layer1: false, layer2: ['reddit', 'telegram'] },
      },
    })

    return {
      telegram: response.data?.payload?.layer2?.find((d) => d.snsPlatform === 'telegram')?.history ?? [],
      reddit: response.data?.payload?.layer2?.find((d) => d.snsPlatform === 'reddit')?.history ?? [],
    }
  },
  {
    server: false,
  },
)

const chartDataMessage = computed<OverviewChartDataset[]>(() => {
  const telegramData = chartDataMessageRaw.value?.telegram ?? []
  const redditData = chartDataMessageRaw.value?.reddit ?? []

  const data = transformToStackedBarSeriesData({ telegram: telegramData, reddit: redditData }).map((item) => ({
    ...item,
    value: item.value.map((v) => v ?? 0),
  }))

  if (data.length === 0) return []

  return [
    {
      type: 'stacked-bar',
      name: ['Telegram', 'Reddit'],
      color: '#007DCF',
      data,
    },
  ]
})

const { data: chartDataCommunity, status: chartCommunityStatus } = useLazyAsyncData(
  `communityMember.${params.slug}`,
  async () => {
    if (!params.slug) return null

    const response = await $apiClient.GET('/nodiens/api/v1/community/history/{metric}/{slug}', {
      params: {
        path: { slug: params.slug, metric: 'MEMBER_COUNT' },
        query: {
          startDate,
          endDate,
          layer1: false,
          layer2: ['reddit', 'telegram'],
        },
      },
    })

    const telegramHistory = (
      response.data?.payload?.layer2?.find((d) => d.snsPlatform === 'telegram')?.history ?? []
    ).map(([time, value]) => ({
      time: format(new Date(time), 'yyyy-MM-dd'),
      value,
    }))

    const redditHistory = (response.data?.payload?.layer2?.find((d) => d.snsPlatform === 'reddit')?.history ?? []).map(
      ([time, value]) => ({
        time: format(new Date(time), 'yyyy-MM-dd'),
        value,
      }),
    )

    return [
      {
        type: 'line',
        name: 'Reddit',
        color: '#EA5C15',
        data: redditHistory,
        showLastMarker: true,
        markerBackgroundColor: 'rgba(234,92,21,1)',
        markerShadowColor: 'rgba(234,92,21,0.35)',
      },
      {
        type: 'line',
        name: 'Telegram',
        color: '#007DCF',
        data: telegramHistory,
        showLastMarker: true,
        markerBackgroundColor: 'rgba(0,125,207,1)',
        markerShadowColor: 'rgba(0,125,207,0.35)',
      },
    ] satisfies OverviewChartDataset[]
  },
  {
    server: false,
  },
)

const { data: chartDataEsgPowerUse, status: chartDataEsgPowerUseStatus } = useLazyAsyncData(
  `esgPowerUse.${params.slug}`,
  async () => {
    if (!overviewData.value?.esgSlug) return []

    const response = await $apiClient.GET('/nodiens/api/v1/esg/energy-consumption/{slug}', {
      params: {
        path: { slug: overviewData.value.esgSlug },
        query: { startDate, endDate, metric: 'POWER_USE_W' },
      },
    })

    const bestGuessSeries = (response.data?.payload.analytic_entries ?? []).map(([time, value]) => ({
      time: format(new Date(time), 'yyyy-MM-dd'),
      value: value.avg,
    }))

    const rangeSeries = (response.data?.payload.analytic_entries ?? []).map(([time, value]) => ({
      time: format(new Date(time), 'yyyy-MM-dd'),
      value: {
        min: value.lower,
        max: value.upper,
      },
    }))

    if (bestGuessSeries.length === 0 && rangeSeries.length === 0) {
      return []
    }

    return [
      {
        type: 'line',
        name: 'Best Guess',
        color: '#4FBD6D',
        data: bestGuessSeries,
        showLastMarker: true,
        markerBackgroundColor: 'rgba(79,189,109,1)',
        markerShadowColor: 'rgba(79,189,109,0.35)',
      },
      {
        type: 'range',
        name: 'Range',
        color: 'rgba(79, 189, 109, 0.15)',
        data: rangeSeries,
      },
    ] satisfies OverviewChartDataset[]
  },
  {
    server: false,
    watch: [() => overviewData.value?.esgSlug],
  },
)

const { data: chartDataEsgEnergyConsumption, status: chartDataEsgEnergyConsumptionStatus } = useLazyAsyncData(
  `esgEnergyConsumption.${params.slug}`,
  async () => {
    if (!overviewData.value?.esgSlug) return []

    const response = await $apiClient.GET('/nodiens/api/v1/esg/energy-consumption/{slug}', {
      params: {
        path: { slug: overviewData.value.esgSlug },
        query: { startDate, endDate, metric: 'ENG_CONS_TX_WH' },
      },
    })

    const bestGuessSeries = (response.data?.payload.analytic_entries ?? []).map(([time, value]) => ({
      time: format(new Date(time), 'yyyy-MM-dd'),
      value: value.avg,
    }))

    const rangeSeries = (response.data?.payload.analytic_entries ?? []).map(([time, value]) => ({
      time: format(new Date(time), 'yyyy-MM-dd'),
      value: {
        min: value.lower,
        max: value.upper,
      },
    }))

    if (bestGuessSeries.length === 0 && rangeSeries.length === 0) {
      return []
    }

    return [
      {
        type: 'line',
        name: 'Best Guess',
        color: '#4FBD6D',
        data: bestGuessSeries,
        showLastMarker: true,
        markerBackgroundColor: 'rgba(79,189,109,1)',
        markerShadowColor: 'rgba(79,189,109,0.35)',
      },
      {
        type: 'range',
        name: 'Range',
        color: 'rgba(79, 189, 109, 0.15)',
        data: rangeSeries,
      },
    ] satisfies OverviewChartDataset[]
  },
  {
    server: false,
    watch: [() => overviewData.value?.esgSlug],
  },
)

const onboardingSteps = computed(() => {
  const steps = [...ONBOARDING_CRYPTO_SUMMARY_PAGE]

  if (!chartDataEsgEnergyConsumption.value?.length) {
    steps.pop()
  }

  if (!chartDataEsgPowerUse.value?.length) {
    steps.pop()
  }

  return steps
})

const searchAsset = async (q: string) => {
  const response = await $apiClient.GET('/nodiens/api/v1/community/asset', {
    params: {
      query: {
        ...(q && { terms: q }),
      },
    },
  })
  return response.data?.payload?.data ?? []
}

const finishOnboarding = async () => {
  await $apiClient.PATCH('/nodiens/api/v1/account/onboarding', {
    body: {
      path: ONBOARDING_PATHS.CRYPTO_SUMMARY,
    },
  })
}

const handleScroll = () => {
  isScrolling.value = true

  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }

  scrollTimeout = setTimeout(() => {
    isScrolling.value = false
  }, 300) // 150ms timeout after scrolling stops
}

const onChartClick = (index: string, returnType: 'url' | 'navigate') => {
  const url = `/community/${index}/${(route.params.slug || '').toString()}`
  if (!isScrolling.value) {
    if (returnType === 'url') {
      return url
    }
    router.push(url)
  }
  return undefined
}

const goToFinancial = () => {
  const now = Date.now()

  // Prevent double click
  if (lastClickTime.value && now - lastClickTime.value < 300) {
    return
  }

  lastClickTime.value = now
  router.push(`/financial?asset1=${route.params.slug}&metric=price&size=100`)
}

const handleAssetChange = async (asset: AssetType) => {
  if (asset?.type === 'STABLECOIN') {
    router.push(`/stablecoin/${asset?.slug}`)
  } else {
    router.push(`/crypto/${asset?.slug}`)
  }
}

onMounted(() => {
  window.addEventListener('touchmove', handleScroll)
})

onBeforeUnmount(() => {
  window.removeEventListener('touchmove', handleScroll)
})
</script>

<template>
  <div class="flex flex-col gap-3 p-5">
    <Head>
      <Title>{{ overviewData?.name ?? '' }} Community Sentiment, Market Dynamics, and Climate Impact live charts</Title>
    </Head>

    <div class="mb-5 flex flex-col gap-3 md:flex-row md:items-center md:justify-between">
      <AssetSelector
        v-model="selectedAsset"
        :search-assets="searchAsset"
        :loading="isOverviewLoading"
        @change="handleAssetChange"
      />

      <span class="text-dark-grey text-base font-medium">Last Update: {{ lastCalculationDate }}</span>
    </div>

    <OverviewSkeleton v-if="isOverviewLoading" />
    <div
      v-else
      id="onboarding-step-1"
      class="flex flex-col gap-3"
    >
      <div class="grid grid-cols-1 gap-3 md:grid-cols-12">
        <div class="col-span-1 md:col-span-8">
          <UCard class="h-full">
            <div class="flex flex-col items-start gap-3 md:flex-row">
              <div class="flex items-center gap-x-2 md:min-w-[80px]">
                <img
                  :src="overviewData?.logo ?? ''"
                  width="80"
                  height="80"
                  class="block h-[50px] w-[50px] md:h-[80px] md:!w-[80px]"
                />

                <h3 class="mb-1 text-3xl font-bold text-black md:hidden dark:text-white">
                  {{ overviewData?.name ?? '' }}
                </h3>
              </div>

              <div class="text-dark-grey flex flex-col flex-wrap text-sm">
                <h3 class="mb-1 hidden text-3xl font-bold text-black md:inline-block dark:text-white">
                  {{ overviewData?.name ?? '' }}
                </h3>
                <p class="dark:text-white">
                  {{ overviewData?.description ?? '' }}
                </p>

                <div class="mt-4 flex items-center gap-x-3">
                  <UButton
                    v-if="overviewData?.website"
                    color="black"
                    leading-icon="i-heroicons-globe-alt"
                    label="Website"
                    :to="overviewData.website"
                    target="_blank"
                  />

                  <UButton
                    v-for="(link, linkIndex) in overviewData?.urls ?? []"
                    :key="linkIndex"
                    leading-icon="i-mdi-github"
                    :to="link.url ?? undefined"
                    :label="titleCase(link.name ?? 'Link')"
                  />
                </div>
              </div>
            </div>
          </UCard>
        </div>
        <div class="col-span-1 flex-col gap-3 md:col-span-4">
          <UCard class="mb-3">
            <div class="mb-4 flex items-center gap-x-1">
              <h3 class="text-neutrals-400 text-base font-medium dark:text-white">Price</h3>
              <InformationPopover icon-class="mt-1.5">
                <TooltipContent>
                  <p v-html="ASSET_CRYPTO_TOOLTIPS.price" />
                </TooltipContent>
              </InformationPopover>
            </div>
            <div class="flex items-center justify-center">
              <span class="text-center text-4xl font-bold text-black dark:text-white">{{
                overviewData?.price ?? '-'
              }}</span>
            </div>
          </UCard>

          <div class="grid grid-cols-1 gap-3 lg:grid-cols-2">
            <CheckFeature
              :allowed="!!isFeatureAllowed('sentiment_metrics.mood_ranking')"
              small
            >
              <UCard>
                <div class="mb-4 flex items-center gap-x-1">
                  <h3 class="text-neutrals-400 text-base font-medium dark:text-white">Mood Ranking</h3>
                  <InformationPopover icon-class="mt-1.5">
                    <TooltipContent>
                      <p v-html="ASSET_CRYPTO_TOOLTIPS.mood_ranking" />
                    </TooltipContent>
                  </InformationPopover>
                </div>
                <div class="flex items-center justify-center">
                  <span class="text-center text-4xl font-bold text-black dark:text-white">
                    {{ overviewData?.moodRankPosition ? `#${overviewData.moodRankPosition}` : '-' }}
                  </span>
                </div>
              </UCard>
            </CheckFeature>
            <CheckFeature
              :allowed="!!isFeatureAllowed('sentiment_metrics.trust_ranking')"
              small
            >
              <UCard>
                <div class="mb-4 flex items-center gap-x-1">
                  <h3 class="text-neutrals-400 text-base font-medium dark:text-white">Trust Ranking</h3>
                  <InformationPopover icon-class="mt-1.5">
                    <TooltipContent>
                      <p v-html="ASSET_CRYPTO_TOOLTIPS.trust_ranking" />
                    </TooltipContent>
                  </InformationPopover>
                </div>
                <div class="flex items-center justify-center">
                  <span class="text-center text-4xl font-bold text-black dark:text-white">
                    {{ overviewData?.trustRankPosition ? `#${overviewData.trustRankPosition}` : '-' }}
                  </span>
                </div>
              </UCard>
            </CheckFeature>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 gap-3 md:grid-cols-3">
        <CheckFeature
          :allowed="!!isFeatureAllowed('sentiment_metrics.messages')"
          small
        >
          <UCard>
            <div class="flex items-center gap-x-1">
              <h3 class="text-neutrals-400 text-base font-medium dark:text-white"># Messages (14d)</h3>
              <InformationPopover icon-class="mt-1.5">
                <TooltipContent>
                  <p>
                    The total number of posts, comments, and messages exchanged across the tracked communities of a
                    crypto asset over the last 14 days. This shows the overall level of activity and interaction within
                    these communities.
                  </p>
                </TooltipContent>
              </InformationPopover>
            </div>
            <div class="flex items-center justify-center">
              <span class="text-center text-4xl font-bold text-black dark:text-white">
                {{ overviewData?.messageCount ?? '-' }}
              </span>
            </div>
          </UCard>
        </CheckFeature>
        <CheckFeature
          :allowed="!!isFeatureAllowed('sentiment_metrics.bots_tracker')"
          small
        >
          <UCard>
            <div class="flex items-center gap-x-1">
              <h3 class="text-neutrals-400 text-base font-medium dark:text-white"># Bots (14d)</h3>
              <InformationPopover icon-class="mt-1.5">
                <TooltipContent>
                  <p v-html="ASSET_CRYPTO_TOOLTIPS.bots" />
                </TooltipContent>
              </InformationPopover>
            </div>
            <div class="flex items-center justify-center">
              <span class="text-center text-4xl font-bold text-black dark:text-white">
                {{ overviewData?.botCount ?? '-' }}
              </span>
            </div>
          </UCard>
        </CheckFeature>
        <CheckFeature
          :allowed="!!isFeatureAllowed('sentiment_metrics.vulgarity_index')"
          small
        >
          <UCard>
            <div class="flex items-center gap-x-1">
              <h3 class="text-neutrals-400 text-base font-medium dark:text-white">Vulgarity Index</h3>
              <InformationPopover icon-class="mt-1.5">
                <TooltipContent>
                  <p v-html="ASSET_CRYPTO_TOOLTIPS.vulgarity_index" />
                </TooltipContent>
              </InformationPopover>
            </div>
            <div class="flex items-center justify-center">
              <span class="text-center text-4xl font-bold text-black dark:text-white">
                {{ overviewData?.badWords ?? '-' }}
              </span>
            </div>
          </UCard>
        </CheckFeature>
      </div>
    </div>

    <div
      id="onboarding-step-2"
      class="flex scroll-mt-60 flex-col gap-3"
    >
      <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
        <CheckFeature
          :allowed="
            !!isFeatureAllowed('sentiment_metrics.mood_index_level_1') &&
            !!isFeatureAllowed('sentiment_metrics.trust_index_level_1')
          "
          :loading="chartDataMoodTrustStatus === 'pending'"
        >
          <div
            id="onboarding-step-3"
            class="col-span-1 scroll-mt-60"
          >
            <CardChart
              title="Mood Index & Trust Index"
              :subtitle="ASSET_CRYPTO_TOOLTIPS.mood_trust_index"
              :legends="LEGEND_MOOD_TRUST"
              :is-loading="chartDataMoodTrustStatus === 'pending'"
              body-class="chart-container v1-4xl:min-h-[510px] h-[338px] 2xl:min-h-[400px]"
            >
              <NuxtLink
                :href="onChartClick('mood', 'url')"
                external
              >
                <OverviewChart :datasets="chartDataMoodTrust || []" />
              </NuxtLink>
            </CardChart>
          </div>
        </CheckFeature>
        <div
          id="onboarding-step-4"
          class="col-span-1 scroll-mt-60"
        >
          <CardChart
            title="Price & Volume"
            :subtitle="ASSET_CRYPTO_TOOLTIPS.price_volume"
            :legends="[...LEGEND_PRICE_VOLUME]"
            :is-loading="priceVolumeStatus === 'pending'"
            body-class="v1-4xl:min-h-[510px] h-[338px] 2xl:min-h-[400px]"
          >
            <NuxtLink
              class="cursor-pointer"
              @click="goToFinancial"
            >
              <NoDataAvailable
                v-if="isChartDataPriceVolumeEmpty"
                class="h-full"
              />
              <div
                v-else
                class="flex h-full flex-col gap-3"
              >
                <div class="flex-1">
                  <OverviewChart
                    :datasets="chartDataPriceVolume?.price || []"
                    :price-formatter="(price) => '$' + numericDisplay(price)"
                    hide-timescale
                  />
                </div>
                <div class="h-[100px] 2xl:h-[120px]">
                  <OverviewChart
                    :datasets="chartDataPriceVolume?.volume || []"
                    :price-formatter="(price) => '$' + numericDisplay(price)"
                    :auto-scale-info-provider="
                      maxVolumeChart > 0
                        ? () => ({
                            priceRange: {
                              minValue: 0,
                              maxValue: maxVolumeChart * 2 * 450000000000, // Need to adjust scale, you can modifying this value
                            },
                            margins: {
                              above: 10,
                              below: 10,
                            },
                          })
                        : undefined
                    "
                  />
                </div>
              </div>
            </NuxtLink>
          </CardChart>
        </div>
      </div>

      <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
        <CheckFeature
          :allowed="!!isFeatureAllowed('sentiment_metrics.messages')"
          :loading="chartMessageStatus === 'pending'"
        >
          <div
            id="onboarding-step-5"
            class="col-span-1 scroll-mt-56"
          >
            <CardChart
              title="# Messages (14d)"
              :subtitle="`The total number of posts, comments, and messages exchanged across the tracked communities of a crypto asset over a rolling 14-day period. This shows the overall level of activity and interaction within these communities.`"
              :legends="LEGEND_PLATFORMS"
              :is-loading="chartMessageStatus === 'pending'"
              body-class="v1-4xl:min-h-[510px] h-[338px] 2xl:min-h-[400px]"
              class="h-full"
            >
              <NuxtLink :href="onChartClick('mood', 'url')">
                <OverviewChart
                  :datasets="chartDataMessage"
                  :decimals="0"
                />
              </NuxtLink>
            </CardChart>
          </div>
        </CheckFeature>
        <CheckFeature
          :allowed="!!isFeatureAllowed('sentiment_metrics.community_size')"
          :loading="chartCommunityStatus === 'pending'"
        >
          <div
            id="onboarding-step-6"
            class="relative col-span-1 scroll-m-56"
          >
            <CardChart
              title="Community Size"
              :subtitle="`The total number of active and inactive members across the tracked communities of a crypto asset. This shows the overall size and potential reach of these communities.`"
              :legends="LEGEND_PLATFORMS"
              :is-loading="chartCommunityStatus === 'pending'"
              body-class="v1-4xl:min-h-[510px] h-[338px] 2xl:min-h-[400px]"
              enable-log-button
            >
              <template #default="{ logarithmic }">
                <NuxtLink :href="onChartClick('mood', 'url')">
                  <OverviewChart
                    :logarithmic="logarithmic"
                    :datasets="chartDataCommunity || []"
                    :decimals="0"
                  />
                </NuxtLink>
              </template>
            </CardChart>
          </div>
        </CheckFeature>
      </div>

      <div
        v-if="!!chartDataEsgPowerUse?.length || !!chartDataEsgEnergyConsumption?.length"
        class="grid grid-cols-1 gap-3 md:grid-cols-2"
      >
        <div
          v-if="!!chartDataEsgPowerUse?.length"
          id="onboarding-step-7"
          class="col-span-1 scroll-mt-64"
        >
          <CardChart
            title="Power Use"
            subtitle="The estimated global power usage of a crypto asset, measured in Watts (W) or their multiples. This is calculated using proprietary methodologies tailored to the specific consensus mechanism employed by the underlying blockchain network of a crypto asset, as well as the type and quantity of hardware utilised."
            :legends="[{ label: 'Power Use', colorClass: 'bg-accent-green-400' }]"
            :is-loading="chartDataEsgPowerUseStatus === 'pending'"
            body-class="v1-4xl:min-h-[510px] h-[338px] 2xl:min-h-[400px]"
          >
            <NuxtLink
              :href="`/climate/energy-consumption/${params.slug}`"
              external
            >
              <OverviewChart
                :datasets="chartDataEsgPowerUse || []"
                :price-formatter="(price) => displayWattage(price)"
              />
            </NuxtLink>
          </CardChart>
        </div>

        <div
          v-if="!!chartDataEsgEnergyConsumption?.length"
          id="onboarding-step-8"
          class="col-span-1 scroll-mt-64"
        >
          <CardChart
            title="Energy Cons/Tx"
            subtitle="The estimated energy consumption per transaction of a crypto asset, measured in Watt-hours (Wh) or their multiples. This is calculated using proprietary methodologies that consider the specific consensus mechanism employed, the type and quantity of hardware utilised, and the real-world transactions being conducted."
            :legends="[{ label: 'Energy Cons/Tx', colorClass: 'bg-accent-green-400' }]"
            :is-loading="chartDataEsgEnergyConsumptionStatus === 'pending'"
            body-class="v1-4xl:min-h-[510px] h-[338px] 2xl:min-h-[400px]"
          >
            <NuxtLink
              :href="`/climate/energy-consumption/${params.slug}`"
              external
            >
              <OverviewChart
                :datasets="chartDataEsgEnergyConsumption || []"
                :price-formatter="(price) => displayWattage(price) + 'h'"
              />
            </NuxtLink>
          </CardChart>
        </div>
      </div>
    </div>

    <ClientOnly>
      <Onboarding
        v-if="overviewStatus !== 'pending' && onboardingStatus !== 'pending' && !onboardingData"
        :steps="onboardingSteps"
        :visible="!onboardingData"
        @on-finish="finishOnboarding"
      />
    </ClientOnly>
  </div>
</template>
