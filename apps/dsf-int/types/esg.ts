import type { paths } from './schema'

export type EsgTableQueryParams = NonNullable<
  paths['/nodiens/api/v1/esg/energy-consumption/']['get']['parameters']['query'] &
    paths['/nodiens/api/v1/esg/carbon-emission/']['get']['parameters']['query']
>

export type EsgAsset =
  paths['/nodiens/api/v1/esg/energy-consumption/asset']['get']['responses']['200']['content']['application/json']['payload']['data'][0]

export type EnergyConsumptionMetric = NonNullable<
  paths['/nodiens/api/v1/esg/energy-consumption/{slug}']['get']['parameters']['query']
>['metric']

export type CarbonEmissionMetric = NonNullable<
  paths['/nodiens/api/v1/esg/carbon-emission/{slug}']['get']['parameters']['query']
>['metric']
